import { 
  BedrockAgentRuntimeClient, 
  RetrieveCommand,
  RetrieveAndGenerateCommand,
  RetrieveAndGenerateCommandInput,
} from '@aws-sdk/client-bedrock-agent-runtime';
import type { BedrockQueryRequest, BedrockQueryResponse } from '../types/index';

// Prompt templates for different insight types
export const PROMPT_TEMPLATES = {
  phase: (context: any) => `You are a clinical trial design expert analyzing similar studies from a knowledge base.

Study Context:
- Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Compound: ${context.isNewCompound ? 'Novel/First-in-class' : 'Existing/Repurposed'}
- Mechanism: ${context.mechanism || 'not specified'}

Based on the retrieved trials, provide your analysis in this EXACT format:

## PHASE RECOMMENDATION
[State the recommended phase clearly: Phase 1, Phase 1/2, Phase 2, Phase 2/3, or Phase 3]

## KEY DESIGN ELEMENTS
- Patient Population: [Target enrollment number and type]
- Duration: [Expected timeline]
- Primary Objective: [Main goal for this phase]

## RATIONALE
[Explain why this phase is appropriate based on the compound type and similar studies]

## CRITICAL CONSIDERATIONS
[List 2-3 key challenges or requirements specific to this phase]

## SIMILAR SUCCESSFUL STUDIES
[Cite specific NCT numbers of comparable trials in this phase]

Important: The PHASE RECOMMENDATION must be a single, clear statement.

$search_results$

$output_format_instructions$`,

  'primary-endpoint': (context: any) => `You are a clinical trial endpoints expert analyzing similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}
- Mechanism of Action: ${context.mechanism || 'not specified'}

Study Design Parameters (Already Selected):
- Design Type: ${context.designType || 'not specified'}
- Blinding: ${context.blinding || 'not specified'}
- Control Type: ${context.controlType || 'not specified'}
- Randomization Ratio: ${context.randomizationRatio || 'not specified'}

Based on the retrieved similar trials below, provide specific primary endpoint recommendations in the following JSON format:

$search_results$

## YOUR ANALYSIS
Provide your recommendations in the following JSON format:

{
  "primaryEndpoint": {
    "recommendation": "The specific primary endpoint (e.g., 'Change in HbA1c from baseline to Week 12')",
    "description": "Detailed description of what this endpoint measures and why it's appropriate",
    "measurementMethod": "How to measure (e.g., 'Laboratory blood test', 'Clinical assessment scale')",
    "timepoint": "When to measure (e.g., 'Baseline and Week 12', 'Every 4 weeks')",
    "clinicalRelevance": "Why this endpoint is clinically meaningful for this indication"
  },
  "measurementSpecifications": {
    "scale": "If using a scale, specify which one",
    "units": "Units of measurement",
    "minimumClinicallyImportantDifference": "MCID if known",
    "analysisMethod": "Primary analysis method (e.g., 'Change from baseline', 'Responder analysis')"
  },
  "alternatives": [
    {
      "endpoint": "Alternative endpoint option 1",
      "rationale": "Why this could be considered",
      "measurementMethod": "How to measure this endpoint",
      "advantages": "Benefits of this endpoint",
      "disadvantages": "Limitations or challenges"
    },
    {
      "endpoint": "Alternative endpoint option 2",
      "rationale": "Why this could be considered",
      "measurementMethod": "How to measure this endpoint",
      "advantages": "Benefits of this endpoint",
      "disadvantages": "Limitations or challenges"
    }
  ],
  "sampleSize": {
    "estimated": "Estimated sample size (e.g., '200 patients')",
    "perArm": "Sample size per treatment arm",
    "assumptions": "Key assumptions (effect size, power, alpha)",
    "adjustments": "Adjustments for dropout, multiplicity, etc."
  },
  "designConsiderations": {
    "blindingImpact": "How the selected blinding affects endpoint assessment",
    "controlTypeAlignment": "How this endpoint aligns with the chosen control type",
    "crossoverSuitability": "If crossover design, is this endpoint appropriate for repeated measures"
  },
  "citations": [
    {
      "nctNumber": "NCT number",
      "title": "Trial title",
      "endpoint": "Primary endpoint used in this trial",
      "outcome": "Whether the trial met its primary endpoint"
    }
  ]
}

Important: The recommendation should be specific and directly applicable, considering the study design parameters already selected.

$output_format_instructions$`,

  'secondary-endpoints': (context: any) => `You are a clinical trial design expert focusing on comprehensive outcome assessment.

Study Context:
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Study Type: ${context.studyType || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'Not yet determined'}

Based on the retrieved clinical trials and studies below, provide specific secondary endpoint recommendations that complement the primary endpoint.

$search_results$

## SECONDARY ENDPOINTS RECOMMENDATION
Provide a concise overview of recommended secondary endpoints that support and complement the primary endpoint.

## EFFICACY ENDPOINTS
List 3-5 efficacy-related secondary endpoints that support the primary efficacy claim:
- Include response rates, remission rates, or time-to-event measures
- Consider clinician-rated and patient-reported outcomes
- Cite specific scales or instruments used in the retrieved trials

## SAFETY ENDPOINTS
List 3-5 safety monitoring endpoints:
- Adverse events and serious adverse events
- Specific safety concerns for this condition/intervention
- Laboratory parameters and vital signs
- Include specific assessment tools (e.g., C-SSRS for suicidality)

## QUALITY OF LIFE ENDPOINTS
List 2-3 QoL or functional measures:
- Disease-specific and general QoL instruments
- Functional assessments relevant to the condition
- Patient-reported outcomes

## EXPLORATORY ENDPOINTS
List 2-3 exploratory endpoints if applicable:
- Biomarkers or pharmacokinetic parameters
- Subgroup analyses
- Novel assessments

## RATIONALE
Explain why these secondary endpoints complement the primary endpoint and support regulatory requirements.

## SIMILAR SUCCESSFUL STUDIES
Cite 2-3 specific NCT numbers from the retrieved studies that used similar endpoint strategies.

$output_format_instructions$`,

  'study-duration': (context: any) => `You are a clinical operations expert analyzing trial timelines.

Study Context:
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Intervention type: ${context.studyType || 'not specified'}

Based on the retrieved clinical trials below, provide specific timeline recommendations.

$search_results$

Analyze the similar trials and provide your recommendations in the following JSON format:

{
  "treatmentDuration": {
    "recommended": "12 weeks",
    "range": "8-16 weeks",
    "rationale": "Most Phase 2 trials for this condition use 12 weeks of active treatment to demonstrate efficacy"
  },
  "followUpPeriod": {
    "recommended": "4 weeks",
    "range": "2-8 weeks",
    "rationale": "Standard safety follow-up period to monitor for delayed adverse events"
  },
  "keyTimepoints": [
    "Baseline (Week 0)",
    "Week 2 (safety assessment)",
    "Week 4 (early efficacy)",
    "Week 8 (interim analysis)",
    "Week 12 (primary endpoint)",
    "Week 16 (follow-up)"
  ],
  "totalDuration": {
    "estimated": "18 months",
    "breakdown": {
      "recruitment": "6 months",
      "treatment": "12 weeks per patient",
      "followUp": "4 weeks per patient",
      "dataAnalysis": "3 months"
    }
  },
  "citations": ["NCT12345678", "NCT23456789", "NCT34567890"]
}

IMPORTANT: 
- Provide specific durations based on the retrieved studies
- Include NCT numbers from studies that used similar timelines
- Keep duration values in practical units (weeks or months)
- Ensure all fields are populated with relevant data

$output_format_instructions$`,

  'site-selection': (context: any) => `You are a clinical trial site selection expert.

Study Context:
- Phase: ${context.phase || 'not specified'}
- Condition: ${context.condition || 'not specified'}
- Population requirements: General adult population

Based on retrieved trials, recommend:
1. Optimal number of sites
2. Geographic distribution strategy
3. Site capability requirements
4. Typical recruitment rates per site

Consider both efficiency and regulatory requirements.

Retrieved Studies:
$search_results$`,

  'safety-monitoring': (context: any) => `You are a clinical safety expert designing monitoring strategies.

Study Context:
- Phase: ${context.phase || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Novel compound: ${context.isNewCompound ? 'Yes' : 'No'}

Based on retrieved trials with similar interventions, recommend:
1. Safety run-in period requirements
2. DSMB necessity and review frequency
3. Safety reporting timelines
4. Stopping rules and futility boundaries
5. Special safety assessments needed

Focus on regulatory expectations and patient safety.

Retrieved Studies:
$search_results$`,

  'design-parameters': (context: any) => `You are a clinical trial design expert analyzing optimal study design parameters.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}
- Mechanism of Action: ${context.mechanism || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'To be determined'}

Based on the retrieved similar trials below, provide specific recommendations for study design parameters in the following JSON format:

$search_results$

## YOUR ANALYSIS
Provide your recommendations in the following JSON format:

{
  "designType": {
    "recommendation": "parallel" | "crossover" | "factorial" | "adaptive" | "platform" | "basket" | "umbrella" | "sequential" | "cluster" | "stepped-wedge" | "n-of-1" | "single-arm",
    "rationale": "Explain why this design type is most appropriate based on phase, condition, and similar trials",
    "examples": ["NCT12345678", "NCT87654321"]
  },
  "blinding": {
    "recommendation": "open-label" | "single-blind" | "double-blind" | "triple-blind" | "quadruple-blind",
    "rationale": "Explain the blinding strategy considering feasibility and regulatory expectations",
    "considerations": ["List key factors like safety monitoring needs", "Intervention characteristics that affect blinding"]
  },
  "controlType": {
    "recommendation": "placebo" | "active-comparator" | "standard-of-care" | "dose-comparison" | "sham" | "historical" | "waitlist" | "usual-care" | "no-treatment" | "none",
    "specificComparator": "If active comparator, specify drug and dose",
    "rationale": "Explain why this control type is appropriate for this phase and indication",
    "ethicalConsiderations": "Address any ethical concerns with the chosen control"
  },
  "randomizationRatio": {
    "recommendation": "1:1" | "2:1" | "3:1" | "1:1:1" | other ratio as string,
    "rationale": "Explain the ratio choice based on phase, safety data needs, and power",
    "sampleSizeImpact": "How this ratio affects required sample size"
  },
  "additionalConsiderations": {
    "stratificationFactors": ["List key stratification variables if applicable"],
    "interimAnalyses": "Whether interim analyses are recommended",
    "adaptiveElements": "Any adaptive design elements to consider",
    "regulatoryPrecedent": "Key regulatory considerations from similar approved trials"
  },
  "citations": [
    {
      "nctNumber": "NCT number",
      "title": "Trial title",
      "relevance": "Why this trial supports your recommendations"
    }
  ]
}

$output_format_instructions$`,

  'demographics': (context: any) => `You are a clinical trial population expert analyzing demographic requirements for optimal patient recruitment.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Study Design: ${context.designType || 'not specified'}

Based on the retrieved similar trials, provide demographic recommendations in this EXACT JSON format:

{
  "ageRange": {
    "recommendedMin": "Minimum age in years as a number (e.g., '18')",
    "recommendedMax": "Maximum age in years as a number (e.g., '65')",
    "rationale": "Why this age range is appropriate for this condition and phase",
    "typicalRange": {
      "min": "Most common minimum age in similar studies",
      "max": "Most common maximum age in similar studies"
    }
  },
  "gender": {
    "recommendation": "all" | "male" | "female",
    "rationale": "Explanation for gender requirement",
    "distribution": "Typical gender distribution in similar studies (e.g., '50% male, 50% female')"
  },
  "specificPopulations": {
    "recommended": ["List of specific populations to consider, e.g., 'Elderly (65+)', 'Treatment-resistant patients'"],
    "avoid": ["Populations typically excluded, e.g., 'Pregnant women', 'Patients with severe renal impairment'"],
    "rationale": "Why these populations are important or should be excluded"
  },
  "healthyVolunteers": {
    "recommendation": true | false,
    "rationale": "Why healthy volunteers should or should not be included",
    "typicalApproach": "What percentage of similar studies include healthy volunteers"
  },
  "demographicConsiderations": [
    "Important demographic factors specific to this condition",
    "Diversity and representation requirements",
    "Special safety considerations for certain populations"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "ageRange": "e.g., '18-65 years'",
      "gender": "all | male | female",
      "enrolledPopulation": "Brief description of who was enrolled"
    }
  ]
}

IMPORTANT: 
1. Base all recommendations on actual demographic profiles from the retrieved similar studies
2. Extract real NCT numbers from retrieved documents for benchmarkStudies
3. Be specific about age ranges using actual numbers, not descriptive terms
4. Consider safety and ethical factors when recommending demographics

$search_results$

$output_format_instructions$`,

  'sample-size': (context: any) => `You are a clinical trial biostatistician and enrollment planning expert analyzing similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}
- Design Type: ${context.designType || 'parallel group'}
- Control Type: ${context.controlType || 'placebo-controlled'}
- Randomization Ratio: ${context.randomizationRatio || '1:1'}

Based on the retrieved similar trials, provide sample size recommendations in this EXACT JSON format:

{
  "targetEnrollment": {
    "recommended": "Total number of patients (e.g., '120')",
    "range": {
      "min": "Minimum reasonable sample size",
      "max": "Maximum reasonable sample size"
    },
    "rationale": "Why this sample size is appropriate for this study"
  },
  "perArm": {
    "treatmentArm": "Number of patients in treatment arm",
    "controlArm": "Number of patients in control arm",
    "otherArms": "If applicable, describe other arms"
  },
  "statisticalAssumptions": {
    "power": "Statistical power (e.g., '80%', '90%')",
    "alpha": "Significance level (e.g., '0.05')",
    "effectSize": "Expected effect size or minimum clinically important difference",
    "dropoutRate": "Expected dropout rate percentage"
  },
  "adjustments": {
    "screenFailRate": "Expected screen failure rate percentage",
    "totalToScreen": "Total patients needed to screen",
    "enrollmentBuffer": "Recommended buffer percentage for dropouts"
  },
  "enrollmentProjections": {
    "monthlyRate": "Expected patients enrolled per month across all sites",
    "timeToFullEnrollment": "Estimated months to complete enrollment",
    "sitesNeeded": "Approximate number of sites needed"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "The actual NCT identifier from the retrieved study (format: NCTxxxxxxxx, e.g., 'NCT12345678') - extract this from the study documents, NOT a description",
      "enrolledPatients": "Number enrolled (e.g., '120')",
      "completionRate": "Percentage who completed (e.g., '85%')"
    }
  ],
  "considerations": [
    "Special considerations for this population",
    "Recruitment challenges to anticipate"
  ]
}

IMPORTANT: 
1. Base your recommendations on the actual enrollment numbers and completion rates from the retrieved studies
2. For benchmarkStudies, you MUST extract the actual NCT identifiers (NCTxxxxxxxx format) from the retrieved documents
3. Do NOT use descriptive text like "Phase 2 study" or "MDD trial" in the nctNumber field
4. Each NCT number should be in the format NCT followed by 8 digits (e.g., NCT12345678)
5. If you cannot find a valid NCT number for a study, do not include it in benchmarkStudies
6. Be specific and quantitative in all fields

$search_results$

$output_format_instructions$`,

  'inclusion-criteria': (context: any) => `You are a clinical trial eligibility expert analyzing inclusion criteria from similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide inclusion criteria recommendations in this EXACT JSON format:

{
  "coreInclusionCriteria": {
    "diagnosis": [
      "Primary diagnosis requirement (e.g., 'Confirmed diagnosis of major depressive disorder per DSM-5')",
      "Duration of diagnosis requirement (e.g., 'Symptoms present for at least 3 months')"
    ],
    "clinicalPresentation": [
      "Symptom severity requirements (e.g., 'HAM-D score ≥18 at screening')",
      "Clinical status requirements (e.g., 'Current major depressive episode')"
    ],
    "treatmentHistory": [
      "Prior treatment requirements (e.g., 'Failed at least one prior antidepressant')",
      "Washout period requirements (e.g., 'No antidepressants for 2 weeks prior')"
    ]
  },
  "demographicCriteria": {
    "age": "Age requirements (e.g., '18-65 years')",
    "gender": "Gender-specific requirements if applicable",
    "other": [
      "BMI requirements if applicable",
      "Language or literacy requirements"
    ]
  },
  "laboratoryRequirements": {
    "required": [
      "Essential lab values (e.g., 'Normal liver function tests')",
      "Required test results (e.g., 'Negative pregnancy test')"
    ],
    "optional": [
      "Preferred but not mandatory lab criteria"
    ]
  },
  "consentRequirements": [
    "Ability to provide written informed consent",
    "Willingness to comply with study procedures",
    "Reliable contraception if applicable"
  ],
  "prioritizedList": [
    "Most critical inclusion criterion #1",
    "Most critical inclusion criterion #2",
    "Most critical inclusion criterion #3",
    "Additional important criteria..."
  ],
  "considerations": [
    "Special considerations for this population",
    "Recruitment facilitators to consider"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "keyInclusionCriteria": [
        "Main inclusion criteria from this study"
      ]
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual inclusion criteria from retrieved similar studies
2. Be specific with numerical thresholds and timeframes
3. Extract real NCT numbers from retrieved documents for benchmarkStudies
4. Prioritize criteria that are most critical for patient safety and study validity
5. Consider feasibility of recruitment when recommending criteria

$search_results$

$output_format_instructions$`,

  'exclusion-criteria': (context: any) => `You are a clinical trial safety expert analyzing exclusion criteria from similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide exclusion criteria recommendations in this EXACT JSON format:

{
  "medicalExclusions": {
    "comorbidities": [
      "Conditions that would exclude (e.g., 'Bipolar disorder or psychotic features')",
      "Serious medical conditions (e.g., 'Unstable cardiovascular disease')"
    ],
    "concomitantMedications": [
      "Prohibited medications (e.g., 'MAO inhibitors within 14 days')",
      "Restricted drug classes (e.g., 'Strong CYP3A4 inhibitors')"
    ],
    "allergiesContraindications": [
      "Drug allergies or hypersensitivities",
      "Contraindications to study drug class"
    ]
  },
  "safetyExclusions": {
    "pregnancy": "Pregnancy and nursing exclusions (e.g., 'Pregnant, nursing, or planning pregnancy')",
    "laboratoryValues": [
      "Unsafe lab ranges (e.g., 'ALT/AST >3x upper limit of normal')",
      "Renal function limits (e.g., 'eGFR <30 mL/min/1.73m²')"
    ],
    "vitalSigns": [
      "Unsafe vital parameters (e.g., 'Uncontrolled hypertension >160/100 mmHg')",
      "ECG abnormalities if applicable"
    ],
    "suicideRisk": "Suicide risk criteria if applicable (e.g., 'Active suicidal ideation with plan')"
  },
  "protocolExclusions": {
    "priorParticipation": "Previous trial restrictions (e.g., 'Participation in investigational drug study within 30 days')",
    "geographicRestrictions": "Location/travel requirements (e.g., 'Unable to attend all study visits')",
    "complianceConcerns": [
      "Factors affecting compliance (e.g., 'History of poor medication adherence')",
      "Substance use restrictions (e.g., 'Alcohol/drug dependence within past year')"
    ]
  },
  "prioritizedList": [
    "Most critical safety exclusion #1",
    "Most critical safety exclusion #2",
    "Most critical safety exclusion #3",
    "Additional important exclusions..."
  ],
  "riskMitigation": [
    "How to effectively screen for these exclusions",
    "Safety monitoring recommendations during the trial"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "keyExclusionCriteria": [
        "Main exclusion criteria from this study"
      ]
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual exclusion criteria from retrieved similar studies
2. Prioritize patient safety above all other considerations
3. Be specific with numerical thresholds and timeframes
4. Extract real NCT numbers from retrieved documents for benchmarkStudies
5. Consider both safety risks and data quality when recommending exclusions
6. Include standard safety exclusions for the drug class/condition

$search_results$

$output_format_instructions$`,

  'study-periods': (context: any) => `You are a clinical trial timeline expert analyzing study duration patterns.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide study period recommendations in this EXACT JSON format:

{
  "studyPeriods": {
    "screening": {
      "duration": "Duration in weeks/days (e.g., '2-4 weeks')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Why this screening duration is appropriate"
    },
    "baseline": {
      "duration": "Duration in weeks/days (e.g., '1 week')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Purpose of baseline period"
    },
    "treatment": {
      "duration": "Duration in weeks/months (e.g., '12 weeks')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Why this treatment duration is optimal"
    },
    "followUp": {
      "duration": "Duration in weeks/months (e.g., '4 weeks')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Importance of follow-up duration"
    },
    "totalDuration": "Total study duration (e.g., '19-21 weeks')"
  },
  "durationFactors": [
    "Key factors affecting study duration",
    "Recruitment challenges to consider",
    "Regulatory requirements"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx)",
      "screeningDuration": "e.g., '4 weeks'",
      "treatmentDuration": "e.g., '12 weeks'",
      "followUpDuration": "e.g., '4 weeks'",
      "totalDuration": "e.g., '20 weeks'"
    }
  ],
  "recommendations": [
    "Consider extending screening if recruitment is challenging",
    "Include run-in period if medication washout needed"
  ]
}

IMPORTANT:
1. Base durations on actual study timelines from retrieved similar trials
2. Use specific time units (weeks, months, days) consistently
3. Extract real NCT numbers from retrieved documents
4. Consider both efficacy assessment needs and practical feasibility
5. Account for the primary endpoint timing requirements

$search_results$

$output_format_instructions$`,

  'visit-schedule': (context: any) => `You are a clinical trial operations expert analyzing visit schedules.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Treatment Duration: ${context.treatmentPeriod || '12 weeks'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide visit schedule recommendations in this EXACT JSON format:

{
  "visitSchedule": [
    {
      "name": "Visit name (e.g., 'Screening', 'Week 4')",
      "timepoint": "When visit occurs (e.g., 'Day -14 to -1', 'Day 28')",
      "window": "Acceptable window (e.g., '± 3 days')",
      "procedures": [
        "Informed consent",
        "Medical history",
        "Physical exam",
        "Laboratory tests"
      ],
      "critical": true,
      "rationale": "Why this visit is important"
    }
  ],
  "totalVisits": "Total number of visits (e.g., 8)",
  "visitFrequency": {
    "intensive": "Frequency during intensive phase (e.g., 'Weekly for first month')",
    "maintenance": "Frequency during maintenance (e.g., 'Monthly thereafter')",
    "followUp": "Frequency during follow-up (e.g., 'Once at 4 weeks post-treatment')"
  },
  "criticalVisits": [
    "Screening",
    "Baseline/Randomization",
    "Primary Endpoint Assessment",
    "End of Treatment"
  ],
  "procedureGroups": {
    "safety": ["Vital signs", "ECG", "Laboratory tests", "Adverse events"],
    "efficacy": ["Primary endpoint assessment", "Secondary endpoints"],
    "pharmacokinetic": ["PK blood draws", "Drug concentration"],
    "administrative": ["Drug dispensing", "Diary review", "Compliance check"]
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx)",
      "totalVisits": "Number of visits",
      "visitFrequency": "Description of frequency"
    }
  ],
  "operationalConsiderations": [
    "Patient burden considerations",
    "Site capacity requirements",
    "Critical assessments timing"
  ]
}

IMPORTANT:
1. Create a complete visit schedule from screening through follow-up
2. Mark visits as critical if they involve randomization, primary endpoints, or safety milestones
3. Group procedures logically by visit
4. Consider patient burden and site feasibility
5. Base on actual visit schedules from retrieved similar studies
6. Include visit windows for flexibility

$search_results$

$output_format_instructions$`,

  'site-planning': (context: any) => `You are a clinical trial operations expert specializing in site selection and planning.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Geographic Scope: ${context.geographicScope || 'not specified'}

Based on the retrieved similar trials, provide site planning recommendations in this EXACT JSON format:

{
  "sitePlanning": {
    "numberOfSites": {
      "recommended": "15-20",
      "minimum": "10",
      "maximum": "25",
      "rationale": "Based on enrollment targets and typical site capacity"
    },
    "recruitmentRate": {
      "expected": "2-3 patients/site/month",
      "range": "1-5 patients/site/month",
      "factors": [
        "Competition from other trials",
        "Disease prevalence in region",
        "Site experience level"
      ]
    },
    "geographicDistribution": {
      "recommended": [
        {
          "region": "North America",
          "countries": ["United States", "Canada"],
          "siteCount": "8-10",
          "rationale": "Strong regulatory infrastructure and patient access"
        },
        {
          "region": "Europe",
          "countries": ["Germany", "United Kingdom", "Spain"],
          "siteCount": "5-7",
          "rationale": "Experienced sites and centralized ethics"
        }
      ],
      "considerations": [
        "Regulatory timeline differences",
        "Language and cultural factors",
        "Cost variations by region"
      ]
    },
    "siteSelectionCriteria": {
      "essential": [
        "Previous experience with condition",
        "Adequate patient population",
        "Required equipment and facilities"
      ],
      "preferred": [
        "Electronic data capture experience",
        "Dedicated research coordinator",
        "Good enrollment track record"
      ]
    }
  },
  "supportingEvidence": [
    {
      "nctNumber": "NCT number",
      "sites": "Number of sites used",
      "enrollmentTime": "Time to full enrollment",
      "relevance": "Why this example is relevant"
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual site numbers from similar successful trials
2. Consider the complexity of the protocol when recommending site numbers
3. Account for regional differences in recruitment rates
4. Be specific about geographic distribution based on the condition
5. Include practical considerations for site selection

$search_results$

$output_format_instructions$`,

  'enrollment-projections': (context: any) => `You are a clinical trial enrollment and retention expert.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Number of Sites: ${context.numberOfSites || 'not specified'}

Based on the retrieved similar trials, provide enrollment projections in this EXACT JSON format:

{
  "enrollmentProjections": {
    "screenFailureRate": {
      "expected": "20-25%",
      "range": "15-30%",
      "commonReasons": [
        "Does not meet inclusion criteria",
        "Meets exclusion criteria",
        "Withdrawal of consent"
      ],
      "mitigationStrategies": [
        "Pre-screening phone calls",
        "Clear communication of requirements",
        "Streamlined screening process"
      ]
    },
    "dropoutRate": {
      "expected": "10-15%",
      "byPhase": {
        "screening": "5%",
        "treatment": "8%",
        "followUp": "2%"
      },
      "commonReasons": [
        "Adverse events",
        "Lack of efficacy",
        "Lost to follow-up",
        "Withdrawal of consent"
      ],
      "retentionStrategies": [
        "Regular patient engagement",
        "Flexible visit scheduling",
        "Transportation assistance"
      ]
    },
    "enrollmentTimeline": {
      "monthsToFullEnrollment": "6-8",
      "rampUpPeriod": "2 months",
      "peakEnrollmentRate": "20 patients/month",
      "assumptions": [
        "All sites activated within 3 months",
        "Steady recruitment after ramp-up",
        "No major protocol amendments"
      ]
    },
    "overEnrollment": {
      "recommendedBuffer": "5-10%",
      "rationale": "Account for dropouts before primary endpoint",
      "strategy": "Continue screening until last patient completes"
    }
  },
  "benchmarkStudies": [
    {
      "nctNumber": "NCT number",
      "actualScreenFailure": "Actual rate achieved",
      "actualDropout": "Actual dropout rate",
      "enrollmentDuration": "Time to full enrollment",
      "lessons": "Key learnings from this trial"
    }
  ]
}

IMPORTANT:
1. Use actual screen failure and dropout rates from similar trials
2. Consider disease severity when projecting dropout rates
3. Account for seasonal variations in enrollment if applicable
4. Be realistic about site activation timelines
5. Include buffer for unexpected delays

$search_results$

$output_format_instructions$`,

  'data-monitoring': (context: any) => `You are a clinical trial data management and monitoring expert.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Phase: ${context.phase || 'not specified'}
- Condition: ${context.condition || 'not specified'}
- Number of Sites: ${context.numberOfSites || 'not specified'}
- Geographic Scope: ${context.geographicScope || 'not specified'}

Based on the retrieved similar trials, provide data management and monitoring recommendations in this EXACT JSON format:

{
  "dataMonitoring": {
    "dataManagement": {
      "recommendedSystem": "edc",
      "systemOptions": {
        "edc": {
          "advantages": [
            "Real-time data access",
            "Built-in edit checks",
            "Reduced transcription errors"
          ],
          "considerations": [
            "Training requirements",
            "Internet connectivity needs",
            "System validation"
          ]
        },
        "paper": {
          "when": "Low-resource settings or simple protocols",
          "requirements": "Double data entry for verification"
        },
        "hybrid": {
          "when": "Mixed site capabilities",
          "approach": "Core data in EDC, source documents on paper"
        }
      },
      "rationale": "EDC standard for Phase 2/3 trials with multiple sites"
    },
    "monitoringApproach": {
      "recommended": "[MUST BE ONE OF: 'on-site', 'remote', 'risk-based', or 'hybrid']",
      "rationale": "Why this approach is recommended for this study",
      "monitoringPlan": {
        "onSite": {
          "frequency": "Quarterly or after every 5 patients",
          "focus": "Consent verification, drug accountability, safety"
        },
        "remote": {
          "frequency": "Monthly",
          "focus": "Data review, query resolution, enrollment tracking"
        },
        "centralized": {
          "frequency": "Continuous",
          "focus": "Statistical monitoring, trend analysis, risk indicators"
        }
      },
      "riskIndicators": [
        "High screen failure rate",
        "Protocol deviations",
        "Data query rate > 10%",
        "Safety signal detection"
      ]
    },
    "dataQuality": {
      "targetMetrics": {
        "queryRate": "< 5% of data points",
        "resolveTime": "< 7 days average",
        "completeness": "> 98% at database lock"
      },
      "qualityControls": [
        "Programmatic edit checks",
        "Medical review of safety data",
        "Statistical monitoring for outliers"
      ]
    },
    "technology": {
      "recommended": [
        "21 CFR Part 11 compliant EDC system",
        "Central randomization system",
        "Drug supply management system",
        "Safety database integration"
      ],
      "optional": [
        "ePRO for patient-reported outcomes",
        "eConsent platform",
        "Risk-based monitoring platform"
      ]
    }
  },
  "industryBenchmarks": [
    {
      "nctNumber": "NCT number",
      "dataSystem": "System used",
      "monitoringApproach": "Approach taken",
      "outcomes": "Quality metrics achieved",
      "relevance": "Why this example applies"
    }
  ]
}

IMPORTANT:
1. Align monitoring intensity with trial risk level
2. Consider regulatory requirements for the phase
3. Balance quality with cost-effectiveness
4. Account for site experience with technology
5. Base on successful approaches from similar trials

$search_results$

$output_format_instructions$`,

  'competitive-landscape': (context: any) => `You are an expert clinical researcher analyzing medical treatments and competitive landscapes.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Name: ${context.drugName || 'investigational product'}
- Drug Class: ${context.drugClass || 'not specified'}
- Route/Formulation: ${context.category || 'not specified'}
- Mechanism of Action: ${context.mechanism || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}

Based on the retrieved similar trials below, provide comprehensive medical problem and competitive landscape analysis in this EXACT JSON format:

$search_results$

## YOUR ANALYSIS
Provide your analysis in the following JSON format:

{
  "medicalProblem": {
    "description": "Clear description of the medical condition and patient impact",
    "epidemiology": "Prevalence data and disease burden (if available from retrieved documents)",
    "unmetNeed": "Current limitations and gaps in treatment"
  },
  "existingTreatments": [
    {
      "treatment": "Current therapy name",
      "mechanism": "How it works",
      "efficacy": "Effectiveness data",
      "limitations": "Safety issues, side effects, or patient burden",
      "marketPosition": "Standard of care, first-line, second-line, etc."
    }
  ],
  "competitiveAdvantages": {
    "primaryBenefit": "Main advantage over existing treatments",
    "safetyProfile": "Expected safety improvements",
    "efficacyExpectation": "Anticipated efficacy benefits",
    "patientBurden": "How this reduces patient burden (dosing, administration, etc.)",
    "differentiatingFactors": "Unique mechanisms or approaches"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "condition": "Medical condition studied",
      "intervention": "Treatment investigated",
      "phase": "Study phase",
      "keyFindings": "Relevant efficacy or safety findings",
      "relevance": "Why this study is relevant for comparison"
    }
  ]
}

IMPORTANT:
1. Extract actual NCT numbers from retrieved documents
2. Be specific and quantitative when data is available
3. Base all recommendations on evidence from retrieved studies
4. Focus on patient impact and clinical meaningfulness
5. Do not include asterisks in your response

$output_format_instructions$`,

  default: (context: any) => `You are a clinical trial design expert.

Analyze the retrieved clinical trials and provide specific, actionable insights for the user's query.
Consider the study context and cite specific examples from successful trials.

Study Context:
${JSON.stringify(context, null, 2)}

$search_results$

$output_format_instructions$`,
};

export class BedrockKnowledgeBaseClient {
  private client: BedrockAgentRuntimeClient;
  private knowledgeBaseId: string;
  
  constructor() {
    this.client = new BedrockAgentRuntimeClient({
      region: process.env.AWS_REGION || 'us-west-2',
    });
    
    this.knowledgeBaseId = process.env.BEDROCK_KNOWLEDGE_BASE_ID || '';
    
    if (!this.knowledgeBaseId) {
      console.warn('BEDROCK_KNOWLEDGE_BASE_ID not configured');
    }
  }
  
  async query(request: BedrockQueryRequest & { field?: string }): Promise<BedrockQueryResponse> {
    const startTime = Date.now();
    
    try {
      // If retrieveOnly is true, just get documents without generation
      if (request.retrieveOnly) {
        return await this.retrieveDocuments(request, startTime);
      }
      
      // Otherwise, retrieve and generate insights
      return await this.retrieveAndGenerate(request, startTime);
    } catch (error) {
      console.error('Knowledge Base query error:', error);
      throw error;
    }
  }
  
  private async retrieveDocuments(
    request: BedrockQueryRequest,
    startTime: number
  ): Promise<BedrockQueryResponse> {
    const command = new RetrieveCommand({
      knowledgeBaseId: this.knowledgeBaseId,
      retrievalQuery: {
        text: request.query,
      },
      retrievalConfiguration: {
        vectorSearchConfiguration: {
          numberOfResults: request.maxResults || 10,
          overrideSearchType: "HYBRID",
        }
      }
    });
    
    const response = await this.client.send(command);
    
    return {
      results: response.retrievalResults?.map(result => ({
        content: result.content?.text || '',
        location: result.location,
        score: result.score || 0,
        metadata: result.metadata,
      })) || [],
      metadata: {
        queryTime: Date.now() - startTime,
        nextToken: response.nextToken,
      }
    };
  }
  
  private async retrieveAndGenerate(
    request: BedrockQueryRequest & { field?: string },
    startTime: number
  ): Promise<BedrockQueryResponse> {
    // Get the appropriate prompt template
    const templateKey = request.field || 'default';
    console.log('=== TEMPLATE SELECTION DEBUG ===');
    console.log('Requested field:', request.field);
    console.log('Template key:', templateKey);
    console.log('Available template keys:', Object.keys(PROMPT_TEMPLATES));
    console.log('Template exists for key:', templateKey in PROMPT_TEMPLATES);
    
    const promptTemplate = PROMPT_TEMPLATES[templateKey as keyof typeof PROMPT_TEMPLATES] 
      || PROMPT_TEMPLATES.default;
    
    const isUsingDefault = promptTemplate === PROMPT_TEMPLATES.default;
    console.log('Using default template:', isUsingDefault);
    console.log('================================');
    
    const formattedPrompt = promptTemplate(request.context || {});
    
    // Log the full prompt for debugging
    console.log('\n=== BEDROCK KNOWLEDGE BASE PROMPT ===');
    console.log('Field:', request.field);
    console.log('Query:', request.query);
    console.log('Context:', JSON.stringify(request.context, null, 2));
    console.log('Full Prompt Template:');
    console.log('----------------------------------------');
    console.log(formattedPrompt);
    console.log('========================================\n');
    
    const command: RetrieveAndGenerateCommandInput = {
      input: {
        text: request.query,
      },
      retrieveAndGenerateConfiguration: {
        type: "KNOWLEDGE_BASE",
        knowledgeBaseConfiguration: {
          knowledgeBaseId: this.knowledgeBaseId,
          // Use the model ID directly - AWS accepts both full ARN and model ID
          modelArn: process.env.BEDROCK_MODEL_ID || 'anthropic.claude-3-5-haiku-20241022-v1:0',
          generationConfiguration: {
            promptTemplate: {
              textPromptTemplate: formattedPrompt,
            },
            inferenceConfig: {
              textInferenceConfig: {
                maxTokens: request.maxTokens || 2048,
                temperature: request.temperature || 0.3,
                topP: 0.9,
              }
            }
          },
          retrievalConfiguration: {
            vectorSearchConfiguration: {
              numberOfResults: 15,
              overrideSearchType: "HYBRID",
            }
          }
        }
      }
    };
    
    const response = await this.client.send(new RetrieveAndGenerateCommand(command));
    
    // Log the full raw response from Bedrock
    console.log('\n=== RAW BEDROCK RESPONSE ===');
    console.log('Full response keys:', Object.keys(response));
    console.log('Output text:', response.output?.text ? `${response.output.text.substring(0, 200)}...` : 'None');
    console.log('Session ID:', response.sessionId);
    console.log('Citations array length:', response.citations?.length || 0);
    
    // Log the entire response object as JSON for deep inspection
    console.log('\nFull response object (stringified):');
    console.log(JSON.stringify(response, null, 2).substring(0, 2000) + '...');
    
    if (response.citations && response.citations.length > 0) {
      console.log('Citations detail:');
      response.citations.forEach((citation, idx) => {
        console.log(`  Citation ${idx + 1}:`);
        console.log('    Text:', citation.generatedResponsePart?.textResponsePart?.text?.substring(0, 100));
        console.log('    References count:', citation.retrievedReferences?.length || 0);
        
        if (citation.retrievedReferences) {
          citation.retrievedReferences.forEach((ref, refIdx) => {
            console.log(`      Reference ${refIdx + 1}:`);
            console.log('        S3 URI:', ref.location?.s3Location?.uri);
            console.log('        Score:', ref.score);
            console.log('        Content preview:', ref.content?.text?.substring(0, 100));
          });
        }
      });
    } else {
      console.log('No citations in response');
    }
    
    console.log('========================================\n');
    
    // Filter out error citations
    const validCitations = response.citations?.filter(c => {
      const citationText = c.generatedResponsePart?.textResponsePart?.text || '';
      // Filter out error messages from Bedrock
      if (citationText.toLowerCase().includes('sorry') || 
          citationText.toLowerCase().includes('unable to assist') ||
          citationText.toLowerCase().includes('cannot help')) {
        console.log('Filtered out error citation:', citationText);
        return false;
      }
      return true;
    }) || [];
    
    // Format the response with field-specific parsing
    const sections = this.parseFieldSpecificResponse(
      response.output?.text || '',
      validCitations,
      request.field || ''
    );
    
    // Extract source documents from valid citations only
    const sources = this.extractSourceDocuments(validCitations);
    
    return {
      output: response.output?.text || '',
      citations: validCitations.map(c => ({
        text: c.generatedResponsePart?.textResponsePart?.text || '',
        references: c.retrievedReferences?.map(ref => ({
          content: ref.content?.text || '',
          location: ref.location,
          metadata: ref.metadata,
          score: ref.score || 0,
        })) || [],
      })),
      sections,
      sources, // Add the extracted sources
      sessionId: response.sessionId,
      metadata: {
        queryTime: Date.now() - startTime,
        modelUsed: process.env.BEDROCK_MODEL_ID,
      }
    };
  }
  
  private parseFieldSpecificResponse(text: string, citations: any[], field: string): any[] {
    // Use field-specific parsing for competitive landscape (JSON format)
    if (field === 'competitive-landscape') {
      return this.parseCompetitiveLandscapeResponse(text, citations);
    }
    
    // Use field-specific parsing for primary endpoint (JSON format)
    if (field === 'primary-endpoint') {
      return this.parsePrimaryEndpointResponse(text, citations);
    }
    
    // Use field-specific parsing for secondary endpoints
    if (field === 'secondary-endpoints') {
      return this.parseSecondaryEndpointsResponse(text, citations);
    }
    
    // Use field-specific parsing for study duration (JSON format)
    if (field === 'study-duration') {
      return this.parseStudyDurationResponse(text, citations);
    }
    
    // Use field-specific parsing for design parameters (JSON format)
    if (field === 'design-parameters') {
      return this.parseDesignParametersResponse(text, citations);
    }
    
    // Use field-specific parsing for sample size (JSON format)
    if (field === 'sample-size') {
      return this.parseSampleSizeResponse(text, citations);
    }
    
    // Use field-specific parsing for demographics (JSON format)
    if (field === 'demographics') {
      return this.parseDemographicsResponse(text, citations);
    }
    
    // Use field-specific parsing for inclusion criteria (JSON format)
    if (field === 'inclusion-criteria') {
      return this.parseInclusionCriteriaResponse(text, citations);
    }
    
    // Use field-specific parsing for exclusion criteria (JSON format)
    if (field === 'exclusion-criteria') {
      return this.parseExclusionCriteriaResponse(text, citations);
    }
    
    // Use field-specific parsing for study periods (JSON format)
    if (field === 'study-periods') {
      return this.parseStudyPeriodsResponse(text, citations);
    }
    
    // Use field-specific parsing for visit schedule (JSON format)
    if (field === 'visit-schedule') {
      return this.parseVisitScheduleResponse(text, citations);
    }
    
    // Use field-specific parsing for operational fields (JSON format)
    if (field === 'site-planning') {
      return this.parseSitePlanningResponse(text, citations);
    }
    
    if (field === 'enrollment-projections') {
      return this.parseEnrollmentProjectionsResponse(text, citations);
    }
    
    if (field === 'data-monitoring') {
      return this.parseDataMonitoringResponse(text, citations);
    }
    
    // Default to standard parsing for other fields
    return this.parseResponseIntoSections(text, citations, field);
  }
  
  private parseSecondaryEndpointsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SECONDARY ENDPOINTS RESPONSE ===');
    console.log('Text length:', text.length);
    
    // Split the text by ## headers to get all sections
    const parts = text.split(/(?=##\s)/);
    console.log('Split text into', parts.length, 'parts');
    
    // Debug: show what headers we found
    const headers = text.match(/##\s+[A-Z\s]+/g);
    console.log('Headers found in text:', headers ? headers.join(' | ') : 'none');
    
    for (const part of parts) {
      if (!part.trim()) continue;
      
      // Extract the header and content
      const headerMatch = part.match(/^##\s+(.+?)(?:\n|$)/);
      if (!headerMatch) continue;
      
      const rawTitle = headerMatch[1].trim();
      const title = rawTitle.replace(/\*+/g, '').trim();
      // Get content after the header
      const content = part.substring(headerMatch[0].length).trim();
      
      console.log(`Processing section: ${title}`);
      console.log(`  Content length: ${content.length}`);
      
      // Skip empty sections
      if (!content) {
        console.log('  Skipping empty section');
        continue;
      }
      
      // Handle the overview section specially
      if (title.includes('SECONDARY ENDPOINTS RECOMMENDATION')) {
        // Extract only the first paragraph as overview
        const paragraphs = content.split(/\n\n+/);
        const overviewContent = paragraphs[0]?.trim() || content.substring(0, 300).trim();
        
        console.log('  -> Found SECONDARY ENDPOINTS RECOMMENDATION overview');
        console.log('     Overview length:', overviewContent.length);
        
        sections.push({
          title: 'Overview',
          content: overviewContent,
          type: 'overview',
          actionable: false,
          citations: this.extractCitations(overviewContent, citations),
          confidence: 0.85,
        });
        continue;
      }
      
      // Only process endpoint sections
      const upperTitle = title.toUpperCase();
      if (upperTitle.includes('EFFICACY ENDPOINTS') || 
          upperTitle.includes('SAFETY ENDPOINTS') || 
          upperTitle.includes('QUALITY OF LIFE ENDPOINTS') || 
          upperTitle.includes('EXPLORATORY ENDPOINTS')) {
        
        // Determine section type
        let sectionType = 'endpoints';
        if (upperTitle.includes('EFFICACY')) sectionType = 'efficacy-endpoints';
        else if (upperTitle.includes('SAFETY')) sectionType = 'safety-endpoints';
        else if (upperTitle.includes('QUALITY OF LIFE')) sectionType = 'qol-endpoints';
        else if (upperTitle.includes('EXPLORATORY')) sectionType = 'exploratory-endpoints';
        
        // Parse numbered endpoints
        const endpoints = this.parseEndpointsList(content);
        
        sections.push({
          title: this.formatSectionTitle(title, sectionType),
          content: content,
          type: sectionType,
          actionable: true,
          endpoints: endpoints,
          citations: this.extractCitations(content, citations),
          confidence: 0.85,
        });
      }
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.endpoints) {
        console.log(`    - Contains ${s.endpoints.length} endpoints`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseStudyDurationResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING STUDY DURATION RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      // Look for JSON object in the text (it might be wrapped in other text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response');
      
      // Create main recommendation section with actionable items
      const recommendationContent = [];
      
      // Add treatment duration as an actionable item
      if (jsonData.treatmentDuration) {
        sections.push({
          title: 'Treatment Duration',
          content: `Recommended: ${jsonData.treatmentDuration.recommended}\nRange: ${jsonData.treatmentDuration.range}\n\n${jsonData.treatmentDuration.rationale || ''}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'studyDuration',
            value: jsonData.treatmentDuration.recommended
          },
          citations: this.extractCitations(jsonData.treatmentDuration.rationale || '', citations),
          confidence: 0.85,
        });
      }
      
      // Add follow-up period as an actionable item
      if (jsonData.followUpPeriod) {
        sections.push({
          title: 'Follow-up Period',
          content: `Recommended: ${jsonData.followUpPeriod.recommended}\nRange: ${jsonData.followUpPeriod.range}\n\n${jsonData.followUpPeriod.rationale || ''}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'followUpPeriod',
            value: jsonData.followUpPeriod.recommended
          },
          citations: this.extractCitations(jsonData.followUpPeriod.rationale || '', citations),
          confidence: 0.85,
        });
      }
      
      // Add key timepoints as information
      if (jsonData.keyTimepoints && jsonData.keyTimepoints.length > 0) {
        sections.push({
          title: 'Key Assessment Timepoints',
          content: jsonData.keyTimepoints.join('\n'),
          type: 'details',
          actionable: false,
          citations: [],
          confidence: 0.80,
        });
      }
      
      // Add total duration breakdown as information
      if (jsonData.totalDuration) {
        let totalContent = `Estimated Total Duration: ${jsonData.totalDuration.estimated}`;
        if (jsonData.totalDuration.breakdown) {
          totalContent += '\n\nBreakdown:';
          for (const [key, value] of Object.entries(jsonData.totalDuration.breakdown)) {
            const label = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
            totalContent += `\n• ${label}: ${value}`;
          }
        }
        sections.push({
          title: 'Total Study Duration',
          content: totalContent,
          type: 'calculation',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // Add citations if available
      if (jsonData.citations && jsonData.citations.length > 0) {
        // Create synthetic citations from the NCT numbers
        const citationList = jsonData.citations
          .filter((nct: string) => nct.match(/NCT\d{8}/))
          .map((nct: string) => ({
            id: nct,
            title: `Referenced study ${nct}`,
            url: `s3://trialynx-clinical-trials-gov/text-documents/${nct.substring(0, 6)}/${nct}.txt`,
            relevance: 0.80,
          }));
        
        // Add citations to the first section
        if (sections.length > 0 && citationList.length > 0) {
          sections[0].citations = citationList;
        }
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'study-duration');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parsePrimaryEndpointResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING PRIMARY ENDPOINT RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response');
      
      // 1. Main Primary Endpoint Recommendation
      if (jsonData.primaryEndpoint) {
        const pe = jsonData.primaryEndpoint;
        let content = `**Recommendation:** **${pe.recommendation}**\n\n${pe.description || ''}`;
        
        if (pe.measurementMethod) {
          content += `\n\n**Measurement Method:** ${pe.measurementMethod}`;
        }
        if (pe.timepoint) {
          content += `\n**Timepoint:** ${pe.timepoint}`;
        }
        if (pe.clinicalRelevance) {
          content += `\n\n**Clinical Relevance:** ${pe.clinicalRelevance}`;
        }
        
        sections.push({
          title: 'Primary Endpoint Recommendation',
          content: content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'primaryEndpoint',
            value: pe.recommendation,
            measurementMethod: pe.measurementMethod,
            timepoint: pe.timepoint
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Measurement Specifications
      if (jsonData.measurementSpecifications) {
        const specs = jsonData.measurementSpecifications;
        const specDetails = [];
        
        if (specs.scale) specDetails.push(`Scale: ${specs.scale}`);
        if (specs.units) specDetails.push(`Units: ${specs.units}`);
        if (specs.minimumClinicallyImportantDifference) {
          specDetails.push(`MCID: ${specs.minimumClinicallyImportantDifference}`);
        }
        if (specs.analysisMethod) {
          specDetails.push(`Analysis Method: ${specs.analysisMethod}`);
        }
        
        if (specDetails.length > 0) {
          sections.push({
            title: 'Measurement Specifications',
            content: specDetails.join('\n'),
            type: 'details',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 3. Alternative Endpoints
      if (jsonData.alternatives && jsonData.alternatives.length > 0) {
        const altContent = jsonData.alternatives.map((alt: any, idx: number) => {
          let altText = `${idx + 1}. **${alt.endpoint}**\n`;
          if (alt.rationale) altText += `   *Rationale:* ${alt.rationale}\n`;
          if (alt.advantages) altText += `   *Advantages:* ${alt.advantages}\n`;
          if (alt.disadvantages) altText += `   *Disadvantages:* ${alt.disadvantages}`;
          return altText;
        }).join('\n\n');
        
        sections.push({
          title: 'Alternative Endpoints',
          content: altContent,
          type: 'alternatives',
          actionable: true,
          alternatives: jsonData.alternatives.map((alt: any) => {
            return {
              endpoint: alt.endpoint,
              rationale: alt.rationale || '',
              measurementMethod: alt.measurementMethod || '',
              actionableData: {
                field: 'primaryEndpoint',
                value: alt.endpoint,  // Use the full endpoint text
                measurementMethod: alt.measurementMethod || ''
              }
            };
          }),
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 4. Sample Size Calculation
      if (jsonData.sampleSize) {
        const ss = jsonData.sampleSize;
        let sampleSizeContent = `Estimated Total: ${ss.estimated}`;
        
        if (ss.perArm) {
          sampleSizeContent += `\nPer Treatment Arm: ${ss.perArm}`;
        }
        if (ss.assumptions) {
          sampleSizeContent += `\n\nAssumptions: ${ss.assumptions}`;
        }
        if (ss.adjustments) {
          sampleSizeContent += `\nAdjustments: ${ss.adjustments}`;
        }
        
        sections.push({
          title: 'Sample Size Calculation',
          content: sampleSizeContent,
          type: 'calculation',
          actionable: false,
          citations: [],
          confidence: 0.70,
        });
      }
      
      // 5. Design Considerations
      if (jsonData.designConsiderations) {
        const dc = jsonData.designConsiderations;
        const considerations = [];
        
        if (dc.blindingImpact) {
          considerations.push(`Blinding Impact: ${dc.blindingImpact}`);
        }
        if (dc.controlTypeAlignment) {
          considerations.push(`Control Type Alignment: ${dc.controlTypeAlignment}`);
        }
        if (dc.crossoverSuitability) {
          considerations.push(`Crossover Suitability: ${dc.crossoverSuitability}`);
        }
        
        if (considerations.length > 0) {
          sections.push({
            title: 'Design-Specific Considerations',
            content: considerations.join('\n\n'),
            type: 'considerations',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // Add citations if available
      if (jsonData.citations && jsonData.citations.length > 0) {
        const citationList = jsonData.citations.map((cit: any) => ({
          id: cit.nctNumber || cit,
          title: cit.title || `Referenced study ${cit.nctNumber || cit}`,
          url: `s3://trialynx-clinical-trials-gov/text-documents/${(cit.nctNumber || cit).substring(0, 6)}/${cit.nctNumber || cit}.txt`,
          relevance: 0.80,
          endpoint: cit.endpoint,
          outcome: cit.outcome,
        }));
        
        // Add citations to the first section
        if (sections.length > 0 && citationList.length > 0) {
          sections[0].citations = citationList;
        }
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'primary-endpoint');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseCompetitiveLandscapeResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING COMPETITIVE LANDSCAPE RESPONSE ===');
    console.log('Text length:', text.length);
    
    // Extract NCT numbers from citations for validation
    const citationNCTNumbers: string[] = [];
    citations.forEach(citation => {
      if (citation.references) {
        citation.references.forEach((ref: any) => {
          if (ref.location?.s3Location?.uri) {
            const nctMatch = ref.location.s3Location.uri.match(/NCT\d+/i);
            if (nctMatch) {
              citationNCTNumbers.push(nctMatch[0].toUpperCase());
            }
          }
        });
      }
    });
    console.log('NCT numbers found in citations:', citationNCTNumbers);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for competitive landscape');
      
      // 1. Medical Problem Description
      if (jsonData.medicalProblem) {
        const mp = jsonData.medicalProblem;
        let content = `**Medical Condition:** ${mp.description}`;
        
        if (mp.epidemiology) {
          content += `\n\n**Disease Burden:** ${mp.epidemiology}`;
        }
        
        if (mp.unmetNeed) {
          content += `\n\n**Unmet Medical Need:** ${mp.unmetNeed}`;
        }
        
        sections.push({
          title: 'Medical Problem Addressed',
          content,
          type: 'information',
          actionable: true,
          actionableData: {
            field: 'medicalProblem',
            value: mp.description,
            epidemiology: mp.epidemiology,
            unmetNeed: mp.unmetNeed
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Existing Treatments
      if (jsonData.existingTreatments && jsonData.existingTreatments.length > 0) {
        const treatments = jsonData.existingTreatments.map((treatment: any) => 
          `**${treatment.treatment}**\n` +
          `- **Mechanism:** ${treatment.mechanism}\n` +
          `- **Efficacy:** ${treatment.efficacy}\n` +
          `- **Limitations:** ${treatment.limitations}\n` +
          `- **Market Position:** ${treatment.marketPosition}`
        ).join('\n\n');
        
        sections.push({
          title: 'Current Treatment Landscape',
          content: `**Existing Therapies:**\n\n${treatments}`,
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Competitive Advantages
      if (jsonData.competitiveAdvantages) {
        const ca = jsonData.competitiveAdvantages;
        let content = `**Primary Advantage:** **${ca.primaryBenefit}**`;
        
        if (ca.safetyProfile) {
          content += `\n\n**Safety Improvements:** ${ca.safetyProfile}`;
        }
        
        if (ca.efficacyExpectation) {
          content += `\n\n**Efficacy Benefits:** ${ca.efficacyExpectation}`;
        }
        
        if (ca.patientBurden) {
          content += `\n\n**Patient Burden Reduction:** ${ca.patientBurden}`;
        }
        
        if (ca.differentiatingFactors) {
          content += `\n\n**Key Differentiators:** ${ca.differentiatingFactors}`;
        }
        
        sections.push({
          title: 'Comparison to Existing Treatments',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'comparisonToExistingTreatments',
            value: `${ca.primaryBenefit}. ${ca.safetyProfile ? ca.safetyProfile + ' ' : ''}${ca.efficacyExpectation ? ca.efficacyExpectation + ' ' : ''}${ca.patientBurden ? ca.patientBurden : ''}`.trim(),
            primaryBenefit: ca.primaryBenefit
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 4. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const studiesList: any[] = [];
        const validCitations: any[] = [];
        
        jsonData.benchmarkStudies.forEach((study: any, index: number) => {
          let nctNumber = study.nctNumber;
          
          // Validate NCT format
          const isValidNCT = /^NCT\d+$/i.test(nctNumber);
          if (!isValidNCT && citationNCTNumbers[index]) {
            // Use citation NCT as fallback
            nctNumber = citationNCTNumbers[index];
          }
          
          const studyInfo = `**${nctNumber}**\n` +
            `- **Condition:** ${study.condition}\n` +
            `- **Intervention:** ${study.intervention}\n` +
            `- **Phase:** ${study.phase}\n` +
            `- **Key Findings:** ${study.keyFindings}\n` +
            `- **Relevance:** ${study.relevance}`;
          
          studiesList.push(studyInfo);
          
          // Create citation if NCT is valid
          if (/^NCT\d+$/i.test(nctNumber)) {
            const nctUpper = nctNumber.toUpperCase();
            validCitations.push({
              id: nctUpper,
              title: `${nctUpper} - ${study.condition}`,
              url: `s3://trialynx-clinical-trials-gov/text-documents/${nctUpper.substring(0, 6)}/${nctUpper}.txt`,
              relevance: 0.85,
            });
          }
        });
        
        sections.push({
          title: 'Benchmark Studies',
          content: `**Relevant Clinical Trials:**\n\n${studiesList.join('\n\n')}`,
          type: 'references',
          actionable: false,
          citations: validCitations,
          confidence: 0.80,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'competitive-landscape');
    }
    
    console.log('Competitive landscape sections created:', sections.length);
    sections.forEach((section, idx) => {
      console.log(`Section ${idx + 1}:`, {
        title: section.title,
        type: section.type,
        actionable: section.actionable,
        hasActionableData: !!section.actionableData,
        citationsCount: section.citations?.length || 0
      });
    });
    console.log('===============================================\n');
    
    return sections;
  }
  
  private parseSampleSizeResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SAMPLE SIZE RESPONSE ===');
    console.log('Text length:', text.length);
    
    // Extract NCT numbers from citations for fallback
    const citationNCTNumbers: string[] = [];
    citations.forEach(citation => {
      if (citation.references) {
        citation.references.forEach((ref: any) => {
          if (ref.location?.s3Location?.uri) {
            const nctMatch = ref.location.s3Location.uri.match(/NCT\d+/i);
            if (nctMatch) {
              citationNCTNumbers.push(nctMatch[0].toUpperCase());
            }
          }
        });
      }
    });
    console.log('NCT numbers found in citations:', citationNCTNumbers);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for sample size');
      
      // 1. Primary Recommendation
      if (jsonData.targetEnrollment) {
        const te = jsonData.targetEnrollment;
        let content = `**Recommended Sample Size:** **${te.recommended}**`;
        
        if (te.range) {
          content += `\n\n**Range:** ${te.range.min} - ${te.range.max} patients`;
        }
        
        if (te.rationale) {
          content += `\n\n**Rationale:** ${te.rationale}`;
        }
        
        sections.push({
          title: 'Target Enrollment',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'targetEnrollment',
            value: te.recommended,
            range: te.range
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Per-Arm Breakdown
      if (jsonData.perArm) {
        const pa = jsonData.perArm;
        const armDetails = [];
        
        if (pa.treatmentArm) armDetails.push(`Treatment Arm: ${pa.treatmentArm} patients`);
        if (pa.controlArm) armDetails.push(`Control Arm: ${pa.controlArm} patients`);
        if (pa.otherArms) armDetails.push(`Other Arms: ${pa.otherArms}`);
        
        if (armDetails.length > 0) {
          sections.push({
            title: 'Allocation by Treatment Arm',
            content: armDetails.join('\n'),
            type: 'details',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 3. Statistical Assumptions
      if (jsonData.statisticalAssumptions) {
        const sa = jsonData.statisticalAssumptions;
        const assumptions = [];
        
        if (sa.power) assumptions.push(`Statistical Power: ${sa.power}`);
        if (sa.alpha) assumptions.push(`Significance Level: ${sa.alpha}`);
        if (sa.effectSize) assumptions.push(`Effect Size: ${sa.effectSize}`);
        if (sa.dropoutRate) assumptions.push(`Expected Dropout Rate: ${sa.dropoutRate}`);
        
        if (assumptions.length > 0) {
          sections.push({
            title: 'Statistical Assumptions',
            content: assumptions.join('\n'),
            type: 'calculation',
            actionable: false,
            citations: [],
            confidence: 0.75,
          });
        }
      }
      
      // 4. Enrollment Adjustments
      if (jsonData.adjustments) {
        const adj = jsonData.adjustments;
        const adjustmentDetails = [];
        
        if (adj.screenFailRate) adjustmentDetails.push(`Screen Failure Rate: ${adj.screenFailRate}`);
        if (adj.totalToScreen) adjustmentDetails.push(`Total to Screen: ${adj.totalToScreen} patients`);
        if (adj.enrollmentBuffer) adjustmentDetails.push(`Enrollment Buffer: ${adj.enrollmentBuffer}`);
        
        if (adjustmentDetails.length > 0) {
          sections.push({
            title: 'Screening & Enrollment Adjustments',
            content: adjustmentDetails.join('\n'),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.70,
          });
        }
      }
      
      // 5. Enrollment Projections
      if (jsonData.enrollmentProjections) {
        const ep = jsonData.enrollmentProjections;
        const projections = [];
        
        if (ep.monthlyRate) projections.push(`Monthly Enrollment Rate: ${ep.monthlyRate}`);
        if (ep.timeToFullEnrollment) projections.push(`Time to Full Enrollment: ${ep.timeToFullEnrollment}`);
        if (ep.sitesNeeded) projections.push(`Sites Needed: ${ep.sitesNeeded}`);
        
        if (projections.length > 0) {
          sections.push({
            title: 'Enrollment Projections',
            content: projections.join('\n'),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.70,
          });
        }
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        // Process benchmark studies and fix NCT numbers
        const processedStudies = jsonData.benchmarkStudies.map((study: any, index: number) => {
          let nctNumber = study.nctNumber;
          
          // Check if the provided NCT number is valid
          const isValidNCT = nctNumber && /^NCT\d+$/i.test(nctNumber);
          
          if (!isValidNCT) {
            // Try to use a citation NCT number as fallback
            if (citationNCTNumbers[index]) {
              console.log(`Replacing invalid NCT "${nctNumber}" with citation NCT: ${citationNCTNumbers[index]}`);
              nctNumber = citationNCTNumbers[index];
            } else {
              console.log(`No valid NCT number for benchmark study: ${nctNumber}`);
              nctNumber = null;
            }
          }
          
          return {
            ...study,
            nctNumber: nctNumber ? nctNumber.toUpperCase() : study.nctNumber,
            hasValidNCT: !!nctNumber && /^NCT\d+$/i.test(nctNumber)
          };
        });
        
        // Create display text including all studies
        const benchmarks = processedStudies.map((study: any) => {
          let studyText = study.hasValidNCT 
            ? `${study.nctNumber}: ${study.enrolledPatients} patients`
            : `${study.nctNumber || 'Study'}: ${study.enrolledPatients} patients`;
          if (study.completionRate) {
            studyText += ` (${study.completionRate} completion)`;
          }
          return studyText;
        }).join('\n');
        
        // Only create citations for studies with valid NCT numbers
        const validCitations = processedStudies
          .filter((study: any) => study.hasValidNCT)
          .map((study: any) => ({
            id: study.nctNumber,
            title: `${study.nctNumber} - ${study.enrolledPatients} patients enrolled`,
            url: `s3://trialynx-clinical-trials-gov/text-documents/${study.nctNumber.substring(0, 6)}/${study.nctNumber}.txt`,
            relevance: 0.80,
          }));
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarks,
          type: 'references',
          actionable: false,
          citations: validCitations,
          confidence: 0.80,
        });
      }
      
      // 7. Special Considerations
      if (jsonData.considerations && jsonData.considerations.length > 0) {
        sections.push({
          title: 'Special Considerations',
          content: jsonData.considerations.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n'),
          type: 'considerations',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'sample-size');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseDemographicsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DEMOGRAPHICS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for demographics');
      
      // 1. Age Range Recommendation
      if (jsonData.ageRange) {
        const ar = jsonData.ageRange;
        let content = `**Recommended Age Range:** **${ar.recommendedMin} - ${ar.recommendedMax} years**`;
        
        if (ar.typicalRange) {
          content += `\n\n**Typical Range in Similar Studies:** ${ar.typicalRange.min} - ${ar.typicalRange.max} years`;
        }
        
        if (ar.rationale) {
          content += `\n\n**Rationale:** ${ar.rationale}`;
        }
        
        sections.push({
          title: 'Age Range',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'ageRange',
            min: ar.recommendedMin,
            max: ar.recommendedMax
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Gender Recommendation
      if (jsonData.gender) {
        const g = jsonData.gender;
        let content = `**Recommendation:** **${g.recommendation === 'all' ? 'All genders' : g.recommendation === 'male' ? 'Male only' : 'Female only'}**`;
        
        if (g.distribution) {
          content += `\n\n**Typical Distribution:** ${g.distribution}`;
        }
        
        if (g.rationale) {
          content += `\n\n**Rationale:** ${g.rationale}`;
        }
        
        sections.push({
          title: 'Gender Requirements',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'gender',
            value: g.recommendation
          },
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Specific Populations
      if (jsonData.specificPopulations) {
        const sp = jsonData.specificPopulations;
        let content = '';
        
        if (sp.recommended && sp.recommended.length > 0) {
          content += `**Recommended Populations:**\n${sp.recommended.map((p: string) => `• ${p}`).join('\n')}`;
        }
        
        if (sp.avoid && sp.avoid.length > 0) {
          if (content) content += '\n\n';
          content += `**Populations to Exclude:**\n${sp.avoid.map((p: string) => `• ${p}`).join('\n')}`;
        }
        
        if (sp.rationale) {
          if (content) content += '\n\n';
          content += `**Rationale:** ${sp.rationale}`;
        }
        
        // Extract the first recommended population as actionable
        const specificPop = sp.recommended && sp.recommended.length > 0 
          ? sp.recommended[0].replace(/[()]/g, '').trim()
          : '';
        
        sections.push({
          title: 'Specific Populations',
          content,
          type: 'information',
          actionable: specificPop !== '',
          actionableData: specificPop ? {
            field: 'specificPopulation',
            value: specificPop
          } : undefined,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 4. Healthy Volunteers
      if (jsonData.healthyVolunteers) {
        const hv = jsonData.healthyVolunteers;
        let content = `**Recommendation:** **${hv.recommendation ? 'Accept healthy volunteers' : 'Patients only (no healthy volunteers)'}**`;
        
        if (hv.typicalApproach) {
          content += `\n\n**Typical Approach:** ${hv.typicalApproach}`;
        }
        
        if (hv.rationale) {
          content += `\n\n**Rationale:** ${hv.rationale}`;
        }
        
        sections.push({
          title: 'Healthy Volunteers',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'healthyVolunteers',
            value: hv.recommendation
          },
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 5. Demographic Considerations
      if (jsonData.demographicConsiderations && jsonData.demographicConsiderations.length > 0) {
        sections.push({
          title: 'Key Demographic Considerations',
          content: jsonData.demographicConsiderations.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n'),
          type: 'considerations',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarks = jsonData.benchmarkStudies.map((study: any) => {
          let studyText = `${study.nctNumber}: ${study.ageRange}, ${study.gender === 'all' ? 'all genders' : study.gender}`;
          if (study.enrolledPopulation) {
            studyText += ` - ${study.enrolledPopulation}`;
          }
          return studyText;
        }).join('\n');
        
        // Only create citations for valid NCT numbers
        const validCitations = jsonData.benchmarkStudies
          .filter((study: any) => study.nctNumber && /^NCT\d+$/i.test(study.nctNumber))
          .map((study: any) => {
            const nctNumber = study.nctNumber.toUpperCase();
            return {
              id: nctNumber,
              title: `${nctNumber} - ${study.ageRange}, ${study.gender}`,
              url: `s3://trialynx-clinical-trials-gov/text-documents/${nctNumber.substring(0, 6)}/${nctNumber}.txt`,
              relevance: 0.80,
            };
          });
        
        sections.push({
          title: 'Similar Study Demographics',
          content: benchmarks,
          type: 'references',
          actionable: false,
          citations: validCitations,
          confidence: 0.80,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'demographics');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseDesignParametersResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DESIGN PARAMETERS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response');
      
      // Create sections for each design parameter with actionable data
      
      // 1. Design Type Section
      if (jsonData.designType) {
        sections.push({
          title: 'Study Design Type',
          content: `Recommendation: ${jsonData.designType.recommendation}\n\n${jsonData.designType.rationale || ''}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'designType',
            value: jsonData.designType.recommendation,
            examples: jsonData.designType.examples || []
          },
          citations: this.extractNCTCitations(jsonData.designType.examples || [], citations),
          confidence: 0.85,
        });
      }
      
      // 2. Blinding Section
      if (jsonData.blinding) {
        sections.push({
          title: 'Blinding Strategy',
          content: `Recommendation: ${jsonData.blinding.recommendation}\n\n${jsonData.blinding.rationale || ''}\n\nKey Considerations:\n${(jsonData.blinding.considerations || []).map((c: string) => `• ${c}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'blinding',
            value: jsonData.blinding.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Control Type Section
      if (jsonData.controlType) {
        let controlContent = `Recommendation: ${jsonData.controlType.recommendation}`;
        if (jsonData.controlType.specificComparator) {
          controlContent += `\nSpecific Comparator: ${jsonData.controlType.specificComparator}`;
        }
        controlContent += `\n\n${jsonData.controlType.rationale || ''}`;
        if (jsonData.controlType.ethicalConsiderations) {
          controlContent += `\n\nEthical Considerations: ${jsonData.controlType.ethicalConsiderations}`;
        }
        
        sections.push({
          title: 'Control Type',
          content: controlContent,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'controlType',
            value: jsonData.controlType.recommendation,
            specificComparator: jsonData.controlType.specificComparator
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 4. Randomization Ratio Section
      if (jsonData.randomizationRatio) {
        sections.push({
          title: 'Randomization Ratio',
          content: `Recommendation: ${jsonData.randomizationRatio.recommendation}\n\n${jsonData.randomizationRatio.rationale || ''}\n\nSample Size Impact: ${jsonData.randomizationRatio.sampleSizeImpact || 'Not specified'}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'randomizationRatio',
            value: jsonData.randomizationRatio.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 5. Additional Considerations Section (non-actionable)
      if (jsonData.additionalConsiderations) {
        const considerations = [];
        if (jsonData.additionalConsiderations.stratificationFactors?.length > 0) {
          considerations.push(`Stratification Factors:\n${jsonData.additionalConsiderations.stratificationFactors.map((f: string) => `• ${f}`).join('\n')}`);
        }
        if (jsonData.additionalConsiderations.interimAnalyses) {
          considerations.push(`Interim Analyses: ${jsonData.additionalConsiderations.interimAnalyses}`);
        }
        if (jsonData.additionalConsiderations.adaptiveElements) {
          considerations.push(`Adaptive Elements: ${jsonData.additionalConsiderations.adaptiveElements}`);
        }
        if (jsonData.additionalConsiderations.regulatoryPrecedent) {
          considerations.push(`Regulatory Precedent: ${jsonData.additionalConsiderations.regulatoryPrecedent}`);
        }
        
        if (considerations.length > 0) {
          sections.push({
            title: 'Additional Considerations',
            content: considerations.join('\n\n'),
            type: 'considerations',
            actionable: false,
            citations: [],
            confidence: 0.75,
          });
        }
      }
      
      // Add citations if available
      if (jsonData.citations && jsonData.citations.length > 0) {
        const citationList = jsonData.citations.map((cit: any) => ({
          id: cit.nctNumber || cit,
          title: cit.title || `Referenced study ${cit.nctNumber || cit}`,
          url: `s3://trialynx-clinical-trials-gov/text-documents/${(cit.nctNumber || cit).substring(0, 6)}/${cit.nctNumber || cit}.txt`,
          relevance: cit.relevance || 0.80,
        }));
        
        // Add citations to the first section
        if (sections.length > 0 && citationList.length > 0) {
          sections[0].citations = citationList;
        }
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'design-parameters');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private extractNCTCitations(nctNumbers: string[], allCitations: any[]): any[] {
    // Create synthetic citations from NCT numbers
    return nctNumbers
      .filter((nct: string) => nct.match(/NCT\d{8}/))
      .map((nct: string) => ({
        id: nct,
        title: `Referenced study ${nct}`,
        url: `s3://trialynx-clinical-trials-gov/text-documents/${nct.substring(0, 6)}/${nct}.txt`,
        relevance: 0.80,
      }));
  }
  
  private parseInclusionCriteriaResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING INCLUSION CRITERIA RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for inclusion criteria');
      
      // 1. Core Inclusion Criteria - Medical
      if (jsonData.coreInclusionCriteria) {
        const core = jsonData.coreInclusionCriteria;
        let criteriaList: string[] = [];
        
        if (core.diagnosis && core.diagnosis.length > 0) {
          criteriaList.push(...core.diagnosis);
        }
        if (core.clinicalPresentation && core.clinicalPresentation.length > 0) {
          criteriaList.push(...core.clinicalPresentation);
        }
        if (core.treatmentHistory && core.treatmentHistory.length > 0) {
          criteriaList.push(...core.treatmentHistory);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Core Medical Criteria',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'bulkInclusionCriteria',
              criteria: criteriaList,
              category: 'medical'
            },
            citations: [],
            confidence: 0.90,
          });
        }
      }
      
      // 2. Demographic Criteria
      if (jsonData.demographicCriteria) {
        const demo = jsonData.demographicCriteria;
        let criteriaList: string[] = [];
        
        if (demo.age) {
          criteriaList.push(demo.age);
        }
        if (demo.gender && demo.gender !== 'Not specified') {
          criteriaList.push(demo.gender);
        }
        if (demo.other && demo.other.length > 0) {
          criteriaList.push(...demo.other.filter((o: string) => o && o !== 'Not specified'));
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Demographic Requirements',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'information',
            actionable: true,
            actionableData: {
              field: 'bulkInclusionCriteria',
              criteria: criteriaList,
              category: 'demographic'
            },
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 3. Laboratory Requirements
      if (jsonData.laboratoryRequirements) {
        const lab = jsonData.laboratoryRequirements;
        let criteriaList: string[] = [];
        
        if (lab.required && lab.required.length > 0) {
          criteriaList.push(...lab.required);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Laboratory Requirements',
            content: `**Required Tests:**\n${criteriaList.map(c => `• ${c}`).join('\n')}`,
            type: 'requirements',
            actionable: true,
            actionableData: {
              field: 'bulkInclusionCriteria',
              criteria: criteriaList,
              category: 'laboratory'
            },
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Prioritized List (Main actionable recommendation)
      if (jsonData.prioritizedList && jsonData.prioritizedList.length > 0) {
        sections.push({
          title: 'Recommended Inclusion Criteria',
          content: `**Priority Criteria:**\n${jsonData.prioritizedList.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'bulkInclusionCriteria',
            criteria: jsonData.prioritizedList,
            category: 'all'
          },
          citations: [],
          confidence: 0.95,
        });
      }
      
      // 5. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n${study.keyInclusionCriteria?.join(', ') || 'Criteria not specified'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.80,
          });
        }
      }
      
      // 6. Considerations
      if (jsonData.considerations && jsonData.considerations.length > 0) {
        sections.push({
          title: 'Special Considerations',
          content: jsonData.considerations.map((c: string) => `• ${c}`).join('\n'),
          type: 'considerations',
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.error('Error parsing inclusion criteria JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Inclusion Criteria Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseExclusionCriteriaResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING EXCLUSION CRITERIA RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for exclusion criteria');
      
      // 1. Medical Exclusions
      if (jsonData.medicalExclusions) {
        const medical = jsonData.medicalExclusions;
        let criteriaList: string[] = [];
        
        if (medical.comorbidities && medical.comorbidities.length > 0) {
          criteriaList.push(...medical.comorbidities);
        }
        if (medical.concomitantMedications && medical.concomitantMedications.length > 0) {
          criteriaList.push(...medical.concomitantMedications);
        }
        if (medical.allergiesContraindications && medical.allergiesContraindications.length > 0) {
          criteriaList.push(...medical.allergiesContraindications);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Medical Exclusions',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'safety',
            actionable: true,
            actionableData: {
              field: 'bulkExclusionCriteria',
              criteria: criteriaList,
              category: 'medical'
            },
            citations: [],
            confidence: 0.95,
          });
        }
      }
      
      // 2. Safety Exclusions
      if (jsonData.safetyExclusions) {
        const safety = jsonData.safetyExclusions;
        let criteriaList: string[] = [];
        
        if (safety.pregnancy) {
          criteriaList.push(safety.pregnancy);
        }
        if (safety.laboratoryValues && safety.laboratoryValues.length > 0) {
          criteriaList.push(...safety.laboratoryValues);
        }
        if (safety.vitalSigns && safety.vitalSigns.length > 0) {
          criteriaList.push(...safety.vitalSigns);
        }
        if (safety.suicideRisk) {
          criteriaList.push(safety.suicideRisk);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Safety Exclusions',
            content: `**Critical Safety Criteria:**\n${criteriaList.map(c => `• ${c}`).join('\n')}`,
            type: 'safety',
            actionable: true,
            actionableData: {
              field: 'bulkExclusionCriteria',
              criteria: criteriaList,
              category: 'safety'
            },
            citations: [],
            confidence: 0.95,
          });
        }
      }
      
      // 3. Protocol Exclusions
      if (jsonData.protocolExclusions) {
        const protocol = jsonData.protocolExclusions;
        let criteriaList: string[] = [];
        
        if (protocol.priorParticipation) {
          criteriaList.push(protocol.priorParticipation);
        }
        if (protocol.geographicRestrictions) {
          criteriaList.push(protocol.geographicRestrictions);
        }
        if (protocol.complianceConcerns && protocol.complianceConcerns.length > 0) {
          criteriaList.push(...protocol.complianceConcerns);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Protocol & Compliance Exclusions',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'protocol',
            actionable: true,
            actionableData: {
              field: 'bulkExclusionCriteria',
              criteria: criteriaList,
              category: 'protocol'
            },
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Prioritized List (Main actionable recommendation)
      if (jsonData.prioritizedList && jsonData.prioritizedList.length > 0) {
        sections.push({
          title: 'Recommended Exclusion Criteria',
          content: `**Priority Exclusions:**\n${jsonData.prioritizedList.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'bulkExclusionCriteria',
            criteria: jsonData.prioritizedList,
            category: 'all'
          },
          citations: [],
          confidence: 0.95,
        });
      }
      
      // 5. Risk Mitigation
      if (jsonData.riskMitigation && jsonData.riskMitigation.length > 0) {
        sections.push({
          title: 'Risk Mitigation Strategies',
          content: jsonData.riskMitigation.map((r: string) => `• ${r}`).join('\n'),
          type: 'guidance',
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n${study.keyExclusionCriteria?.join(', ') || 'Criteria not specified'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.80,
          });
        }
      }
      
    } catch (error) {
      console.error('Error parsing exclusion criteria JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Exclusion Criteria Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseStudyPeriodsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING STUDY PERIODS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for study periods');
      
      // 1. Study Periods Recommendation
      if (jsonData.studyPeriods) {
        const periods = jsonData.studyPeriods;
        let content = '';
        const actionableData: any = {};
        
        if (periods.screening) {
          content += `**Screening Period:** ${periods.screening.duration}`;
          if (periods.screening.typicalRange) {
            content += ` (typical: ${periods.screening.typicalRange})`;
          }
          content += '\n';
          if (periods.screening.rationale) {
            content += `Rationale: ${periods.screening.rationale}\n`;
          }
          content += '\n';
          actionableData.screeningPeriod = periods.screening.duration;
        }
        
        if (periods.baseline) {
          content += `**Baseline Period:** ${periods.baseline.duration}`;
          if (periods.baseline.typicalRange) {
            content += ` (typical: ${periods.baseline.typicalRange})`;
          }
          content += '\n';
          if (periods.baseline.rationale) {
            content += `Rationale: ${periods.baseline.rationale}\n`;
          }
          content += '\n';
          actionableData.baselinePeriod = periods.baseline.duration;
        }
        
        if (periods.treatment) {
          content += `**Treatment Period:** ${periods.treatment.duration}`;
          if (periods.treatment.typicalRange) {
            content += ` (typical: ${periods.treatment.typicalRange})`;
          }
          content += '\n';
          if (periods.treatment.rationale) {
            content += `Rationale: ${periods.treatment.rationale}\n`;
          }
          content += '\n';
          actionableData.treatmentPeriod = periods.treatment.duration;
        }
        
        if (periods.followUp) {
          content += `**Follow-up Period:** ${periods.followUp.duration}`;
          if (periods.followUp.typicalRange) {
            content += ` (typical: ${periods.followUp.typicalRange})`;
          }
          content += '\n';
          if (periods.followUp.rationale) {
            content += `Rationale: ${periods.followUp.rationale}\n`;
          }
          content += '\n';
          actionableData.followUpPeriod = periods.followUp.duration;
        }
        
        if (periods.totalDuration) {
          content += `\n**Total Study Duration:** ${periods.totalDuration}`;
        }
        
        sections.push({
          title: 'Recommended Study Periods',
          content: content.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'studyPeriods',
            ...actionableData
          },
          citations: [],
          confidence: 0.90,
        });
      }
      
      // 2. Duration Factors
      if (jsonData.durationFactors && jsonData.durationFactors.length > 0) {
        sections.push({
          title: 'Duration Considerations',
          content: jsonData.durationFactors.map((f: string) => `• ${f}`).join('\n'),
          type: 'considerations',
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n` +
            `• Screening: ${study.screeningDuration || 'N/A'}\n` +
            `• Treatment: ${study.treatmentDuration || 'N/A'}\n` +
            `• Follow-up: ${study.followUpDuration || 'N/A'}\n` +
            `• Total: ${study.totalDuration || 'N/A'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.85,
          });
        }
      }
      
      // 4. Recommendations
      if (jsonData.recommendations && jsonData.recommendations.length > 0) {
        sections.push({
          title: 'Additional Recommendations',
          content: jsonData.recommendations.map((r: string) => `• ${r}`).join('\n'),
          type: 'guidance',
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.error('Error parsing study periods JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Study Period Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseVisitScheduleResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING VISIT SCHEDULE RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for visit schedule');
      
      // 1. Visit Schedule
      if (jsonData.visitSchedule && jsonData.visitSchedule.length > 0) {
        const visits = jsonData.visitSchedule.map((visit: any) => ({
          name: visit.name,
          timepoint: visit.timepoint,
          procedures: visit.procedures || [],
          critical: visit.critical || false,
        }));
        
        // Format visits for display
        const visitContent = visits.map((v: any, i: number) => 
          `**${i + 1}. ${v.name}** (${v.timepoint})${v.critical ? ' 🔴 Critical' : ''}\n` +
          `   Procedures: ${v.procedures.join(', ')}`
        ).join('\n\n');
        
        sections.push({
          title: 'Recommended Visit Schedule',
          content: visitContent,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'visitSchedule',
            visits: visits
          },
          citations: [],
          confidence: 0.90,
        });
      }
      
      // 2. Visit Frequency
      if (jsonData.visitFrequency) {
        const freq = jsonData.visitFrequency;
        let content = '';
        
        if (freq.intensive) {
          content += `**Intensive Phase:** ${freq.intensive}\n`;
        }
        if (freq.maintenance) {
          content += `**Maintenance Phase:** ${freq.maintenance}\n`;
        }
        if (freq.followUp) {
          content += `**Follow-up Phase:** ${freq.followUp}`;
        }
        
        if (content) {
          sections.push({
            title: 'Visit Frequency',
            content: content.trim(),
            type: 'information',
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 3. Critical Visits
      if (jsonData.criticalVisits && jsonData.criticalVisits.length > 0) {
        sections.push({
          title: 'Critical Visits',
          content: '🔴 **Must-have visits for study integrity:**\n' + 
                   jsonData.criticalVisits.map((v: string) => `• ${v}`).join('\n'),
          type: 'important',
          citations: [],
          confidence: 0.95,
        });
      }
      
      // 4. Procedure Groups
      if (jsonData.procedureGroups) {
        const groups = jsonData.procedureGroups;
        let content = '';
        
        if (groups.safety && groups.safety.length > 0) {
          content += `**Safety Assessments:**\n${groups.safety.map((p: string) => `• ${p}`).join('\n')}\n\n`;
        }
        if (groups.efficacy && groups.efficacy.length > 0) {
          content += `**Efficacy Assessments:**\n${groups.efficacy.map((p: string) => `• ${p}`).join('\n')}\n\n`;
        }
        if (groups.pharmacokinetic && groups.pharmacokinetic.length > 0) {
          content += `**PK Assessments:**\n${groups.pharmacokinetic.map((p: string) => `• ${p}`).join('\n')}\n\n`;
        }
        if (groups.administrative && groups.administrative.length > 0) {
          content += `**Administrative:**\n${groups.administrative.map((p: string) => `• ${p}`).join('\n')}`;
        }
        
        if (content) {
          sections.push({
            title: 'Common Procedures by Type',
            content: content.trim(),
            type: 'details',
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 5. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n` +
            `• Total Visits: ${study.totalVisits || 'N/A'}\n` +
            `• Frequency: ${study.visitFrequency || 'N/A'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.85,
          });
        }
      }
      
      // 6. Operational Considerations
      if (jsonData.operationalConsiderations && jsonData.operationalConsiderations.length > 0) {
        sections.push({
          title: 'Operational Considerations',
          content: jsonData.operationalConsiderations.map((c: string) => `• ${c}`).join('\n'),
          type: 'considerations',
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.error('Error parsing visit schedule JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Visit Schedule Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseSitePlanningResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SITE PLANNING RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for site planning');
      
      // 1. Site Planning Recommendations
      if (jsonData.sitePlanning) {
        const planning = jsonData.sitePlanning;
        
        // Number of Sites
        if (planning.numberOfSites) {
          const sites = planning.numberOfSites;
          let content = `**Recommended:** ${sites.recommended} sites\n`;
          if (sites.minimum && sites.maximum) {
            content += `**Range:** ${sites.minimum} to ${sites.maximum} sites\n`;
          }
          if (sites.rationale) {
            content += `**Rationale:** ${sites.rationale}`;
          }
          
          sections.push({
            title: 'Number of Sites',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'numberOfSites',
              value: sites.recommended
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Recruitment Rate
        if (planning.recruitmentRate) {
          const rate = planning.recruitmentRate;
          let content = `**Expected:** ${rate.expected}\n`;
          if (rate.range) {
            content += `**Range:** ${rate.range}\n`;
          }
          if (rate.factors && rate.factors.length > 0) {
            content += `\n**Key Factors:**\n${rate.factors.map((f: string) => `• ${f}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Recruitment Rate',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'recruitmentRate',
              value: rate.expected
            },
            citations: [],
            confidence: 0.85,
          });
        }
        
        // Geographic Distribution
        if (planning.geographicDistribution) {
          const geo = planning.geographicDistribution;
          
          if (geo.recommended && geo.recommended.length > 0) {
            const regions = geo.recommended.map((r: any) => 
              `**${r.region}:** ${r.siteCount} sites\n` +
              `Countries: ${r.countries.join(', ')}\n` +
              `${r.rationale ? `Rationale: ${r.rationale}` : ''}`
            ).join('\n\n');
            
            const countries: Record<string, number> = {};
            geo.recommended.forEach((r: any) => {
              const sitesPerCountry = Math.ceil(parseInt(r.siteCount) / r.countries.length);
              r.countries.forEach((country: string) => {
                countries[country] = sitesPerCountry;
              });
            });
            
            sections.push({
              title: 'Geographic Distribution',
              content: regions,
              type: 'recommendation',
              actionable: true,
              actionableData: {
                field: 'sitesPerCountry',
                value: countries
              },
              citations: [],
              confidence: 0.85,
            });
          }
          
          if (geo.considerations && geo.considerations.length > 0) {
            sections.push({
              title: 'Geographic Considerations',
              content: geo.considerations.map((c: string) => `• ${c}`).join('\n'),
              type: 'information',
              citations: [],
              confidence: 0.80,
            });
          }
        }
        
        // Site Selection Criteria
        if (planning.siteSelectionCriteria) {
          const criteria = planning.siteSelectionCriteria;
          let content = '';
          
          if (criteria.essential && criteria.essential.length > 0) {
            content += `**Essential Requirements:**\n${criteria.essential.map((c: string) => `• ${c}`).join('\n')}\n\n`;
          }
          if (criteria.preferred && criteria.preferred.length > 0) {
            content += `**Preferred Qualifications:**\n${criteria.preferred.map((c: string) => `• ${c}`).join('\n')}`;
          }
          
          if (content) {
            sections.push({
              title: 'Site Selection Criteria',
              content: content.trim(),
              type: 'guidance',
              citations: [],
              confidence: 0.85,
            });
          }
        }
      }
      
      // Supporting Evidence
      if (jsonData.supportingEvidence && jsonData.supportingEvidence.length > 0) {
        const validEvidence = jsonData.supportingEvidence.filter((e: any) => 
          e.nctNumber && e.nctNumber.match(/NCT\d+/)
        );
        
        if (validEvidence.length > 0) {
          const evidenceContent = validEvidence.map((e: any) => 
            `• **${e.nctNumber}**: ${e.sites || 'N/A'} sites, enrolled in ${e.enrollmentTime || 'N/A'}`
          ).join('\n');
          
          sections.push({
            title: 'Supporting Evidence',
            content: evidenceContent,
            type: 'evidence',
            citations: validEvidence.map((e: any) => ({
              nctNumber: e.nctNumber,
              title: e.relevance || 'Supporting study',
              relevance: e.relevance
            })),
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.error('Failed to parse site planning JSON:', error);
      // Fallback to text parsing
      return this.parseResponseIntoSections(text, citations, 'site-planning');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseEnrollmentProjectionsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING ENROLLMENT PROJECTIONS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for enrollment projections');
      
      // 1. Enrollment Projections
      if (jsonData.enrollmentProjections) {
        const projections = jsonData.enrollmentProjections;
        
        // Screen Failure Rate
        if (projections.screenFailureRate) {
          const failure = projections.screenFailureRate;
          let content = `**Expected Rate:** ${failure.expected}\n`;
          if (failure.range) {
            content += `**Typical Range:** ${failure.range}\n`;
          }
          if (failure.commonReasons && failure.commonReasons.length > 0) {
            content += `\n**Common Reasons:**\n${failure.commonReasons.map((r: string) => `• ${r}`).join('\n')}`;
          }
          if (failure.mitigationStrategies && failure.mitigationStrategies.length > 0) {
            content += `\n\n**Mitigation Strategies:**\n${failure.mitigationStrategies.map((s: string) => `• ${s}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Screen Failure Rate',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'screenFailureRate',
              value: failure.expected
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Dropout Rate
        if (projections.dropoutRate) {
          const dropout = projections.dropoutRate;
          let content = `**Expected Rate:** ${dropout.expected}\n`;
          
          if (dropout.byPhase) {
            content += `\n**By Study Phase:**\n`;
            if (dropout.byPhase.screening) content += `• Screening: ${dropout.byPhase.screening}\n`;
            if (dropout.byPhase.treatment) content += `• Treatment: ${dropout.byPhase.treatment}\n`;
            if (dropout.byPhase.followUp) content += `• Follow-up: ${dropout.byPhase.followUp}\n`;
          }
          
          if (dropout.commonReasons && dropout.commonReasons.length > 0) {
            content += `\n**Common Reasons:**\n${dropout.commonReasons.map((r: string) => `• ${r}`).join('\n')}`;
          }
          
          if (dropout.retentionStrategies && dropout.retentionStrategies.length > 0) {
            content += `\n\n**Retention Strategies:**\n${dropout.retentionStrategies.map((s: string) => `• ${s}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Dropout Rate',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'dropoutRate',
              value: dropout.expected
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Enrollment Timeline
        if (projections.enrollmentTimeline) {
          const timeline = projections.enrollmentTimeline;
          let content = `**Time to Full Enrollment:** ${timeline.monthsToFullEnrollment}\n`;
          if (timeline.rampUpPeriod) {
            content += `**Ramp-up Period:** ${timeline.rampUpPeriod}\n`;
          }
          if (timeline.peakEnrollmentRate) {
            content += `**Peak Enrollment Rate:** ${timeline.peakEnrollmentRate}\n`;
          }
          if (timeline.assumptions && timeline.assumptions.length > 0) {
            content += `\n**Key Assumptions:**\n${timeline.assumptions.map((a: string) => `• ${a}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Enrollment Timeline',
            content: content.trim(),
            type: 'projection',
            citations: [],
            confidence: 0.85,
          });
        }
        
        // Over-enrollment Buffer
        if (projections.overEnrollment) {
          const over = projections.overEnrollment;
          let content = `**Recommended Buffer:** ${over.recommendedBuffer}\n`;
          if (over.rationale) {
            content += `**Rationale:** ${over.rationale}\n`;
          }
          if (over.strategy) {
            content += `**Strategy:** ${over.strategy}`;
          }
          
          sections.push({
            title: 'Over-enrollment Planning',
            content: content.trim(),
            type: 'guidance',
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validBenchmarks = jsonData.benchmarkStudies.filter((b: any) => 
          b.nctNumber && b.nctNumber.match(/NCT\d+/)
        );
        
        if (validBenchmarks.length > 0) {
          const benchmarkContent = validBenchmarks.map((b: any) => 
            `• **${b.nctNumber}**:\n` +
            `  Screen Failure: ${b.actualScreenFailure || 'N/A'}\n` +
            `  Dropout: ${b.actualDropout || 'N/A'}\n` +
            `  Enrollment Duration: ${b.enrollmentDuration || 'N/A'}\n` +
            `  ${b.lessons ? `Lessons: ${b.lessons}` : ''}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content: benchmarkContent,
            type: 'evidence',
            citations: validBenchmarks.map((b: any) => ({
              nctNumber: b.nctNumber,
              title: b.lessons || 'Benchmark study',
              relevance: b.lessons
            })),
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.error('Failed to parse enrollment projections JSON:', error);
      // Fallback to text parsing
      return this.parseResponseIntoSections(text, citations, 'enrollment-projections');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseDataMonitoringResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DATA MONITORING RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for data monitoring');
      
      // 1. Data Monitoring Recommendations
      if (jsonData.dataMonitoring) {
        const monitoring = jsonData.dataMonitoring;
        
        // Data Management System
        if (monitoring.dataManagement) {
          const dm = monitoring.dataManagement;
          let content = `**Recommended System:** ${dm.recommendedSystem?.toUpperCase() || 'EDC'}\n`;
          
          if (dm.rationale) {
            content += `**Rationale:** ${dm.rationale}\n`;
          }
          
          if (dm.systemOptions) {
            if (dm.systemOptions.edc) {
              const edc = dm.systemOptions.edc;
              content += `\n**EDC System:**\n`;
              if (edc.advantages && edc.advantages.length > 0) {
                content += `Advantages:\n${edc.advantages.map((a: string) => `• ${a}`).join('\n')}\n`;
              }
              if (edc.considerations && edc.considerations.length > 0) {
                content += `Considerations:\n${edc.considerations.map((c: string) => `• ${c}`).join('\n')}\n`;
              }
            }
          }
          
          sections.push({
            title: 'Data Management System',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'dataManagement',
              value: dm.recommendedSystem || 'edc'
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Monitoring Approach
        if (monitoring.monitoringApproach) {
          const approach = monitoring.monitoringApproach;
          let content = `**Recommended Approach:** ${approach.recommended?.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) || 'Risk-Based Monitoring'}\n`;
          
          if (approach.monitoringPlan) {
            const plan = approach.monitoringPlan;
            content += `\n**Monitoring Plan:**\n`;
            
            if (plan.onSite) {
              content += `\n**On-Site Monitoring:**\n`;
              content += `• Frequency: ${plan.onSite.frequency}\n`;
              content += `• Focus: ${plan.onSite.focus}\n`;
            }
            
            if (plan.remote) {
              content += `\n**Remote Monitoring:**\n`;
              content += `• Frequency: ${plan.remote.frequency}\n`;
              content += `• Focus: ${plan.remote.focus}\n`;
            }
            
            if (plan.centralized) {
              content += `\n**Centralized Monitoring:**\n`;
              content += `• Frequency: ${plan.centralized.frequency}\n`;
              content += `• Focus: ${plan.centralized.focus}\n`;
            }
          }
          
          if (approach.riskIndicators && approach.riskIndicators.length > 0) {
            content += `\n**Risk Indicators to Monitor:**\n${approach.riskIndicators.map((r: string) => `• ${r}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Monitoring Approach',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'monitoringApproach',
              value: approach.recommended || 'risk-based'
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Data Quality Metrics
        if (monitoring.dataQuality) {
          const quality = monitoring.dataQuality;
          let content = '';
          
          if (quality.targetMetrics) {
            const metrics = quality.targetMetrics;
            content += `**Target Metrics:**\n`;
            if (metrics.queryRate) content += `• Query Rate: ${metrics.queryRate}\n`;
            if (metrics.resolveTime) content += `• Query Resolution Time: ${metrics.resolveTime}\n`;
            if (metrics.completeness) content += `• Data Completeness: ${metrics.completeness}\n`;
          }
          
          if (quality.qualityControls && quality.qualityControls.length > 0) {
            content += `\n**Quality Control Measures:**\n${quality.qualityControls.map((c: string) => `• ${c}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Data Quality Standards',
            content: content.trim(),
            type: 'guidance',
            citations: [],
            confidence: 0.85,
          });
        }
        
        // Technology Stack
        if (monitoring.technology) {
          const tech = monitoring.technology;
          let content = '';
          
          if (tech.recommended && tech.recommended.length > 0) {
            content += `**Recommended Systems:**\n${tech.recommended.map((t: string) => `• ${t}`).join('\n')}`;
          }
          
          if (tech.optional && tech.optional.length > 0) {
            content += `\n\n**Optional Enhancements:**\n${tech.optional.map((t: string) => `• ${t}`).join('\n')}`;
          }
          
          if (content) {
            sections.push({
              title: 'Technology Requirements',
              content: content.trim(),
              type: 'information',
              citations: [],
              confidence: 0.80,
            });
          }
        }
      }
      
      // Industry Benchmarks
      if (jsonData.industryBenchmarks && jsonData.industryBenchmarks.length > 0) {
        const validBenchmarks = jsonData.industryBenchmarks.filter((b: any) => 
          b.nctNumber && b.nctNumber.match(/NCT\d+/)
        );
        
        if (validBenchmarks.length > 0) {
          const benchmarkContent = validBenchmarks.map((b: any) => 
            `• **${b.nctNumber}**:\n` +
            `  System: ${b.dataSystem || 'N/A'}\n` +
            `  Monitoring: ${b.monitoringApproach || 'N/A'}\n` +
            `  ${b.outcomes ? `Outcomes: ${b.outcomes}` : ''}`
          ).join('\n\n');
          
          sections.push({
            title: 'Industry Benchmarks',
            content: benchmarkContent,
            type: 'evidence',
            citations: validBenchmarks.map((b: any) => ({
              nctNumber: b.nctNumber,
              title: b.relevance || 'Industry benchmark',
              relevance: b.relevance
            })),
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.error('Failed to parse data monitoring JSON:', error);
      // Fallback to text parsing
      return this.parseResponseIntoSections(text, citations, 'data-monitoring');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseResponseIntoSections(text: string, citations: any[], field: string): any[] {
    const sections = [];
    
    // Define section types based on keywords
    const sectionTypeMap: Record<string, { type: string; actionable: boolean }> = {
      'RECOMMENDATION': { type: 'recommendation', actionable: true },
      'PRIMARY RECOMMENDATION': { type: 'recommendation', actionable: true },
      'PHASE RECOMMENDATION': { type: 'recommendation', actionable: true },
      'SECONDARY ENDPOINTS RECOMMENDATION': { type: 'overview', actionable: false },
      'EFFICACY ENDPOINTS': { type: 'efficacy-endpoints', actionable: true },
      'SAFETY ENDPOINTS': { type: 'safety-endpoints', actionable: true },
      'QUALITY OF LIFE ENDPOINTS': { type: 'qol-endpoints', actionable: true },
      'EXPLORATORY ENDPOINTS': { type: 'exploratory-endpoints', actionable: true },
      'MEASUREMENT DETAILS': { type: 'details', actionable: false },
      'KEY DESIGN ELEMENTS': { type: 'details', actionable: false },
      'RATIONALE': { type: 'rationale', actionable: false },
      'ALTERNATIVE OPTIONS': { type: 'alternatives', actionable: true },
      'SAMPLE SIZE ESTIMATE': { type: 'calculation', actionable: false },
      'SIMILAR STUDIES': { type: 'references', actionable: false },
      'CRITICAL CONSIDERATIONS': { type: 'considerations', actionable: false },
      'SIMILAR SUCCESSFUL STUDIES': { type: 'references', actionable: false },
    };
    
    // Split by markdown headers (##)
    const sectionRegex = /^#{2}\s+(.+?)$/gm;
    const matches = [...text.matchAll(sectionRegex)];
    
    if (matches.length > 0) {
      // Parse structured sections
      for (let i = 0; i < matches.length; i++) {
        const match = matches[i];
        const nextMatch = matches[i + 1];
        const startIndex = match.index! + match[0].length;
        const endIndex = nextMatch ? nextMatch.index! : text.length;
        
        const rawTitle = match[1]!.trim();
        const title = rawTitle.replace(/\*+/g, '').trim(); // Remove markdown bold
        const content = text.substring(startIndex, endIndex).trim();
        
        // Skip empty sections
        if (!content) {
          continue;
        }
        
        // Determine section type and actionability
        const upperTitle = title.toUpperCase();
        let sectionInfo = { type: 'information', actionable: false };
        
        for (const [key, value] of Object.entries(sectionTypeMap)) {
          if (upperTitle.includes(key)) {
            sectionInfo = value;
            break;
          }
        }
        
        // Check if this is an endpoints section that needs special parsing
        const isEndpointSection = sectionInfo.type.endsWith('-endpoints');
        
        if (isEndpointSection) {
          // Parse numbered endpoints list
          const endpoints = this.parseEndpointsList(content);
          
          sections.push({
            title: this.formatSectionTitle(title, sectionInfo.type),
            content: content, // Keep original for reference
            type: sectionInfo.type,
            actionable: sectionInfo.actionable,
            endpoints: endpoints, // Add parsed endpoints array
            citations: this.extractCitations(content, citations),
            confidence: 0.85,
          });
        } else {
          // Clean and format the content normally
          const cleanContent = this.formatSectionContent(content, sectionInfo.type);
          
          sections.push({
            title: this.formatSectionTitle(title, sectionInfo.type),
            content: cleanContent,
            type: sectionInfo.type,
            actionable: sectionInfo.actionable,
            citations: this.extractCitations(content, citations),
            confidence: sectionInfo.type === 'recommendation' ? 0.90 : 0.85,
          });
        }
      }
    }
    
    // Fallback for unstructured responses
    if (sections.length === 0) {
      // Try to extract a recommendation from the text
      const recommendationMatch = text.match(/(?:recommend|suggest|advise)[\s\S]{0,500}/i);
      
      if (recommendationMatch) {
        sections.push({
          title: 'Recommendation',
          content: recommendationMatch[0],
          type: 'recommendation',
          actionable: true,
          citations: this.extractCitations(text, citations),
          confidence: 0.75,
        });
      } else {
        sections.push({
          title: this.getDefaultTitle(field),
          content: text,
          type: 'information',
          actionable: false,
          citations: this.extractCitations(text, citations),
          confidence: 0.70,
        });
      }
    }
    
    return sections;
  }
  
  private parseEndpointsList(content: string): any[] {
    const endpoints = [];
    
    // Pattern to match numbered endpoints with bold titles
    // Matches: **1. Title** or **Title** after a number
    const lines = content.split('\n');
    let currentEndpoint: any = null;
    
    for (const line of lines) {
      // Check for numbered item with bold title (e.g., "**1. Response Rate**" or "1. **Response Rate**")
      const numberedMatch = line.match(/^(?:\*\*)?(\d+)\.\s*\*?\*?(.+?)\*?\*?$/);
      
      if (numberedMatch) {
        // Save previous endpoint if exists
        if (currentEndpoint) {
          endpoints.push(currentEndpoint);
        }
        
        // Start new endpoint
        const title = numberedMatch[2].replace(/\*/g, '').trim();
        currentEndpoint = {
          title: title,
          description: []
        };
      } else if (currentEndpoint && line.trim()) {
        // Add description lines (skip empty lines)
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('-') || trimmedLine.startsWith('•')) {
          // Remove bullet and add to description
          currentEndpoint.description.push(trimmedLine.substring(1).trim());
        } else if (!trimmedLine.startsWith('#')) {
          // Add non-header lines to description
          currentEndpoint.description.push(trimmedLine);
        }
      }
    }
    
    // Don't forget the last endpoint
    if (currentEndpoint) {
      endpoints.push(currentEndpoint);
    }
    
    // Format each endpoint for display
    return endpoints.map(ep => ({
      title: ep.title,
      content: ep.description.join('\n'),
      actionable: true
    }));
  }
  
  private formatSectionContent(content: string, sectionType: string): string {
    // Clean up the content based on section type
    let formatted = content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('##'))
      .join('\n');
    
    // Special formatting for recommendation sections
    if (sectionType === 'recommendation') {
      // Remove brackets and clean up
      formatted = formatted
        .replace(/^\[/, '')
        .replace(/\]$/, '')
        .trim();
    }
    
    // Format bullet points consistently
    formatted = formatted
      .replace(/^- /gm, '• ')
      .replace(/^\* /gm, '• ')
      .replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold markers but keep text
    
    return formatted;
  }
  
  private formatSectionTitle(title: string, sectionType: string): string {
    // Create user-friendly titles
    const titleMap: Record<string, string> = {
      'recommendation': 'Recommendation',
      'details': 'Implementation Details',
      'rationale': 'Scientific Rationale',
      'alternatives': 'Alternative Options',
      'calculation': 'Statistical Considerations',
      'references': 'Supporting Studies',
      'considerations': 'Key Considerations',
    };
    
    // If we have a mapped title for this type, use it
    if (titleMap[sectionType]) {
      return titleMap[sectionType];
    }
    
    // Otherwise, clean up the original title
    return title
      .replace(/^(PRIMARY |PHASE |KEY |CRITICAL |SIMILAR )/i, '')
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
  
  private extractCitations(text: string, citations: any[]): any[] {
    const nctRegex = /NCT\d{8}/g;
    const mentionedNCTs = text.match(nctRegex) || [];
    
    // First try to match mentioned NCTs with retrieved references
    const citationList = citations
      .flatMap(c => c.retrievedReferences || [])
      .filter(ref => {
        const uri = ref.location?.s3Location?.uri || '';
        return mentionedNCTs.some(nct => uri.includes(nct));
      })
      .map(ref => ({
        id: this.extractNCTId(ref.location?.s3Location?.uri),
        title: this.extractTitle(ref.content?.text),
        url: ref.location?.s3Location?.uri || '',
        relevance: ref.score || 0.5,
      }))
      .slice(0, 5);
    
    // If citations were found from retrieved references, return them
    if (citationList.length > 0) {
      return citationList;
    }
    
    // If no citations from retrieved references but NCT numbers are mentioned in text,
    // create synthetic citations from the mentioned NCT numbers
    if (mentionedNCTs.length > 0 && citationList.length === 0) {
      console.log('Creating synthetic citations for mentioned NCT numbers:', mentionedNCTs);
      return mentionedNCTs
        .filter((nct, index, self) => self.indexOf(nct) === index) // Remove duplicates
        .slice(0, 5) // Limit to 5
        .map(nct => ({
          id: nct,
          title: `Referenced study ${nct}`,
          url: `s3://trialynx-clinical-trials-gov/text-documents/${nct.substring(0, 6)}/${nct}.txt`,
          relevance: 0.75, // Default relevance for synthetic citations
        }));
    }
    
    // If no specific citations found but we have citations with references, include top ones
    if (citations.length > 0) {
      const topRefs = citations
        .flatMap(c => c.retrievedReferences || [])
        .slice(0, 3);
      
      if (topRefs.length > 0) {
        return topRefs.map(ref => ({
          id: this.extractNCTId(ref.location?.s3Location?.uri),
          title: this.extractTitle(ref.content?.text),
          url: ref.location?.s3Location?.uri || '',
          relevance: ref.score || 0.5,
        }));
      }
    }
    
    return [];
  }
  
  private extractNCTId(uri: string): string {
    const match = uri?.match(/NCT\d{8}/);
    return match ? match[0] : 'Unknown';
  }
  
  private extractTitle(content: string): string {
    const lines = content?.split('\n') || [];
    const titleLine = lines.find(line => 
      line.includes('Title:') || line.includes('Official Title:')
    );
    if (titleLine) {
      return titleLine.replace(/^.*?Title:\s*/i, '').substring(0, 200).trim();
    }
    return content?.substring(0, 100) + '...' || 'Clinical Trial';
  }
  
  private extractSourceDocuments(citations: any[]): any[] {
    const sources: any[] = [];
    const seenNCTs = new Set<string>();
    
    // Extract unique source documents from all citations
    citations.forEach(citation => {
      if (citation.retrievedReferences) {
        citation.retrievedReferences.forEach((ref: any) => {
          const s3Uri = ref.location?.s3Location?.uri;
          if (s3Uri) {
            const nctId = this.extractNCTId(s3Uri);
            
            // Only add if we haven't seen this NCT ID yet
            if (nctId && nctId !== 'Unknown' && !seenNCTs.has(nctId)) {
              seenNCTs.add(nctId);
              
              const content = ref.content?.text || '';
              
              // Extract status - look for it anywhere in the content
              let status = 'Unknown';
              const statusMatch = content.match(/Status:\s*([A-Z]+)/i);
              if (statusMatch && statusMatch[1]) {
                status = statusMatch[1].trim();
                // Handle common truncations
                if (status === 'UNKNOWN') status = 'Unknown';
                else if (status === 'COMPLETE') status = 'COMPLETED';
                else if (status === 'RECRUIT') status = 'RECRUITING';
              }
              
              // Extract study type - look for it anywhere in the content
              let studyType = 'Unknown';
              const studyTypeMatch = content.match(/Study Type:\s*([A-Z]+)/i);
              if (studyTypeMatch && studyTypeMatch[1]) {
                studyType = studyTypeMatch[1].trim();
                // Handle common truncations
                if (studyType === 'INTERVENTIONA') studyType = 'INTERVENTIONAL';
                else if (studyType === 'OBSERVATION') studyType = 'OBSERVATIONAL';
                else if (studyType === 'UNKNOWN') studyType = 'Unknown';
              }
              
              // Get content preview - keep it simple, just clean it up a bit
              let excerpt = undefined;
              const lines = content.split('\n');
              
              // Look for meaningful content keywords
              const meaningfulContent = lines.find(line => {
                const lower = line.toLowerCase();
                return lower.includes('primary outcome') ||
                       lower.includes('secondary endpoint') ||
                       lower.includes('remission') ||
                       lower.includes('response') ||
                       lower.includes('progression') ||
                       lower.includes('survival') ||
                       lower.includes('safety') ||
                       lower.includes('efficacy');
              });
              
              if (meaningfulContent) {
                // If we found meaningful content, use that
                excerpt = meaningfulContent.trim().substring(0, 200);
              } else {
                // Otherwise, just grab the first non-header content
                const nonHeaderLines = lines.filter(line => {
                  const trimmed = line.trim();
                  return trimmed && 
                         !trimmed.match(/^-+$/) &&
                         !trimmed.match(/^=+$/) &&
                         !trimmed.match(/^[A-Z\s]{0,30}$/) && // Skip short all-caps lines
                         trimmed.length > 20; // Skip very short lines
                });
                
                if (nonHeaderLines.length > 0) {
                  excerpt = nonHeaderLines[0].trim().substring(0, 200);
                }
              }
              
              sources.push({
                nctId,
                status,
                studyType,
                s3Uri,
                excerpt,
                score: ref.score, // Keep score but won't display if undefined
              });
            }
          }
        });
      }
    });
    
    // Sort by score (relevance) if available
    sources.sort((a, b) => (b.score || 0) - (a.score || 0));
    
    return sources;
  }
  
  private getDefaultTitle(field: string): string {
    const titles: Record<string, string> = {
      'phase': 'Phase Recommendation',
      'primary-endpoint': 'Primary Endpoint Recommendation',
      'secondary-endpoints': 'Secondary Endpoints Recommendation',
      'study-duration': 'Study Duration Recommendation',
      'site-selection': 'Site Selection Strategy',
      'visit-schedule': 'Visit Schedule Recommendation',
      'safety-monitoring': 'Safety Monitoring Plan',
    };
    return titles[field] || 'Recommendation';
  }
}