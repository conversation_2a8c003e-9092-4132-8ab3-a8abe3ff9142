import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { generateMockInsights } from "~/services/mock/trial-data";
import { env } from "~/env";
import type { GeneratedInsights } from "~/types/trial-design";

// In-memory insights storage for development
const insightsCache = new Map<string, GeneratedInsights>();

export const insightsRouter = createTRPCRouter({
  generateInsights: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      selectedStudyIds: z.array(z.string()).min(1, "At least one study must be selected"),
      focusAreas: z.array(
        z.enum(["design", "population", "intervention", "endpoints", "operational"])
      ).optional(),
    }))
    .mutation(async ({ input }) => {
      const useMockData = env.NEXT_PUBLIC_USE_MOCK_DATA === "true";
      
      if (useMockData) {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Generate mock insights
        const insights = generateMockInsights(input.selectedStudyIds);
        
        // Filter by focus areas if specified
        if (input.focusAreas && input.focusAreas.length > 0) {
          insights.successPatterns = insights.successPatterns.filter(
            p => input.focusAreas!.includes(p.category)
          );
          
          insights.recommendations = insights.recommendations.filter(
            r => {
              const area = r.area.toLowerCase();
              return input.focusAreas!.some(fa => area.includes(fa));
            }
          );
        }
        
        // Cache the insights
        insightsCache.set(input.sessionId, insights);
        
        return insights;
      } else {
        // TODO: Implement actual AWS Bedrock insights generation
        throw new Error("AWS integration not yet configured. Please use mock data for testing.");
      }
    }),

  getInsights: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input }) => {
      const insights = insightsCache.get(input.sessionId);
      
      if (!insights) {
        return null;
      }
      
      return insights;
    }),

  getRecommendations: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      category: z.enum(["sample-size", "duration", "design", "endpoints", "sites"]),
    }))
    .query(async ({ input }) => {
      const insights = insightsCache.get(input.sessionId);
      
      if (!insights) {
        throw new Error("No insights found for this session");
      }
      
      // Filter recommendations by category
      const categoryMap: Record<string, string[]> = {
        "sample-size": ["Sample Size", "Enrollment", "Participants"],
        "duration": ["Duration", "Timeline", "Period"],
        "design": ["Design", "Randomization", "Blinding", "Assignment"],
        "endpoints": ["Endpoints", "Outcomes", "Measures"],
        "sites": ["Sites", "Centers", "Locations", "Multi-site"],
      };
      
      const keywords = categoryMap[input.category] ?? [];
      
      const filteredRecommendations = insights.recommendations.filter(r => 
        keywords.some(keyword => 
          r.area.includes(keyword) || r.recommendation.includes(keyword)
        )
      );
      
      return {
        recommendations: filteredRecommendations,
        statistics: insights.statistics,
      };
    }),

  exportInsights: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      format: z.enum(["json", "pdf", "markdown"]),
    }))
    .mutation(async ({ input }) => {
      const insights = insightsCache.get(input.sessionId);
      
      if (!insights) {
        throw new Error("No insights found for this session");
      }
      
      switch (input.format) {
        case "json":
          return {
            format: "json",
            content: JSON.stringify(insights, null, 2),
            mimeType: "application/json",
            filename: `insights-${input.sessionId}.json`,
          };
          
        case "markdown":
          // Generate markdown report
          const markdown = `
# Clinical Trial Design Insights

Generated: ${new Date(insights.generatedAt).toLocaleDateString()}
Based on ${insights.basedOnStudies.length} studies

## Success Patterns

${insights.successPatterns.map(p => `
### ${p.description}
- **Frequency**: ${p.frequency}% of successful studies
- **Confidence**: ${(p.confidence * 100).toFixed(0)}%
- **Impact**: ${p.impact}
`).join("\n")}

## Risk Factors

${insights.riskFactors.map(r => `
### ${r.factor}
- **Impact Level**: ${r.impact}
- **Frequency**: ${r.frequency}%
- **Mitigation**: ${r.mitigation}
`).join("\n")}

## Recommendations

${insights.recommendations.map(r => `
### ${r.area}
**Recommendation**: ${r.recommendation}

**Rationale**: ${r.rationale}

**Priority**: ${r.priority}

**Alternatives**:
${r.alternatives.map(a => `- ${a}`).join("\n")}
`).join("\n")}

## Key Statistics

- **Median Enrollment**: ${insights.statistics.medianEnrollment} participants
- **Enrollment Range**: ${insights.statistics.enrollmentRange.min} - ${insights.statistics.enrollmentRange.max}
- **Median Duration**: ${insights.statistics.medianDurationMonths} months
- **Success Rate**: ${(insights.statistics.successRate * 100).toFixed(0)}%
- **Completion Rate**: ${(insights.statistics.completionRate * 100).toFixed(0)}%

## Key Takeaways

${insights.keyTakeaways.map(t => `- ${t}`).join("\n")}
`;
          
          return {
            format: "markdown",
            content: markdown,
            mimeType: "text/markdown",
            filename: `insights-${input.sessionId}.md`,
          };
          
        case "pdf":
          // For PDF, we would need a library like jsPDF or puppeteer
          // For now, return a placeholder
          throw new Error("PDF export not yet implemented. Please use JSON or Markdown format.");
          
        default:
          throw new Error(`Unsupported format: ${input.format}`);
      }
    }),

  clearInsights: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .mutation(async ({ input }) => {
      insightsCache.delete(input.sessionId);
      
      return {
        success: true,
        clearedSessionId: input.sessionId,
      };
    }),
});