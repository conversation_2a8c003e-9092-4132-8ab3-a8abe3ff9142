output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = aws_api_gateway_stage.main.invoke_url
}

output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.main.id
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.query_knowledge_base.function_name
}

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.query_knowledge_base.arn
}

output "jwt_authorizer_function_name" {
  description = "Name of the JWT Authorizer Lambda function"
  value       = aws_lambda_function.jwt_authorizer.function_name
}