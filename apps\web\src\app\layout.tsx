import "~/styles/globals.css";

import { type <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";

import { TRPCReactProvider } from "~/trpc/react";

export const metadata: Metadata = {
  title: "TriaLynx Insights - AI-Powered Clinical Trial Design",
  description: "Design successful clinical trials with data-driven insights from thousands of existing studies",
  keywords: ["clinical trials", "study design", "clinical research", "trial protocol", "FDA", "medical research"],
  authors: [{ name: "TriaLynx Team" }],
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${inter.variable}`}>
      <body className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 font-sans antialiased">
        <TRPCReactProvider>{children}</TRPCReactProvider>
        <Toaster position="top-center" richColors />
      </body>
    </html>
  );
}
