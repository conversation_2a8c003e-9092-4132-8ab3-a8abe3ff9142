"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Textarea } from "~/components/ui/textarea";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ChevronLeft, Users, Plus, X, Sparkles, Calculator, Globe, Shield } from "lucide-react";
import type { Gender } from "~/types/trial-design";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { SourceStudiesViewerPortal } from "~/components/insights/SourceStudiesViewerPortal";
import type { InsightSection } from "~/types/trial-design";

export default function PopulationPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    targetEnrollment: store.discovery.population.targetEnrollment || "",
    ageMin: store.discovery.population.ageMin || 18,
    ageMax: store.discovery.population.ageMax || 65,
    gender: store.discovery.population.gender || "all" as Gender,
    specificPopulation: store.discovery.population.specificPopulation || "",
    geographicScope: store.discovery.population.geographicScope || "national" as "local" | "national" | "international",
    inclusionCriteria: store.discovery.population.inclusionCriteria || [],
    exclusionCriteria: store.discovery.population.exclusionCriteria || [],
    healthyVolunteers: store.discovery.population.healthyVolunteers || false,
  });

  const [newInclusion, setNewInclusion] = useState("");
  const [newExclusion, setNewExclusion] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: InsightSection[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  const [sourceStudiesViewerOpen, setSourceStudiesViewerOpen] = useState(false);
  const [sourceStudiesViewerSources, setSourceStudiesViewerSources] = useState<any[]>([]);
  
  // Get cached insights from store
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined, // Clear progress status when real data arrives
        progressMessages: [],
      };
      
      // Update local state
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      // Cache the insights in the store
      store.cacheInsights(variables.field, insightsPayload);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
      // Clear progress status on error
      if (error && typeof error === 'object' && 'data' in error) {
        const errorData = error as any;
        const field = errorData.data?.field;
        if (field) {
          setInsightsData(prev => ({
            ...prev,
            [field]: {
              sections: [],
              sources: [],
              progressStatus: undefined,
              progressMessages: [],
            }
          }));
        }
      }
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("population");
      router.push("/study/new/timeline");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleContinue = () => {
    if (formData.ageMin && formData.ageMax && formData.ageMin > formData.ageMax) {
      toast.error("Minimum age cannot be greater than maximum age");
      return;
    }

    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Update store
    store.updateDiscovery({ population: formData });

    // Save to backend
    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        population: formData,
      },
    });
  };

  const addInclusionCriteria = () => {
    if (newInclusion.trim()) {
      setFormData({
        ...formData,
        inclusionCriteria: [...formData.inclusionCriteria, newInclusion.trim()],
      });
      setNewInclusion("");
    }
  };

  const removeInclusionCriteria = (index: number) => {
    setFormData({
      ...formData,
      inclusionCriteria: formData.inclusionCriteria.filter((_, i) => i !== index),
    });
  };

  const addExclusionCriteria = () => {
    if (newExclusion.trim()) {
      setFormData({
        ...formData,
        exclusionCriteria: [...formData.exclusionCriteria, newExclusion.trim()],
      });
      setNewExclusion("");
    }
  };

  const removeExclusionCriteria = (index: number) => {
    setFormData({
      ...formData,
      exclusionCriteria: formData.exclusionCriteria.filter((_, i) => i !== index),
    });
  };

  const getInsightsPanelTitle = (field: string): string => {
    const titles: Record<string, string> = {
      'sample-size': 'Sample Size Calculation',
      'demographics': 'Demographics Recommendations',
      'inclusion-criteria': 'Inclusion Criteria Recommendations',
      'exclusion-criteria': 'Exclusion Criteria Recommendations',
    };
    return titles[field] || 'Insights';
  };

  const handleGetInsights = (field: string, forceRefresh = false) => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Check cache first unless forcing refresh
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }

    // Map field to appropriate query
    const queries: Record<string, string> = {
      'sample-size': `What sample size is typical for ${store.discovery.condition || 'this condition'} in ${store.discovery.phase || 'Phase 2'} trials?`,
      'demographics': `What are the typical demographic requirements for ${store.discovery.condition || 'this condition'} in ${store.discovery.phase || 'Phase 2'} trials?`,
      'inclusion-criteria': `What are typical inclusion criteria for ${store.discovery.condition || 'clinical trials'}?`,
      'exclusion-criteria': `What are typical exclusion criteria for ${store.discovery.condition || 'clinical trials'}?`,
    };

    const query = queries[field] || `Provide insights for ${field}`;

    // Show panel immediately with loading state
    setActiveInsightsPanel(field);
    
    // Set initial progress status
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Initializing search...',
        progressMessages: [],
      }
    }));

    // Progressive status updates (10-12 second timeline)
    const progressUpdates = [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching knowledge base...' },
      { delay: 3500, message: 'Analyzing relevant studies...' },
      { delay: 6000, message: 'Extracting key insights...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    // Update progress status progressively
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          // Only update if we're still loading (haven't received results yet)
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });

    const context = {
      studyType: store.discovery.studyType || "drug",
      condition: store.discovery.condition || "",
      drugClass: store.discovery.intervention?.drugClass || "",
      phase: store.discovery.phase || "",
      primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
      designType: store.discovery.design?.designType || "parallel",
      controlType: store.discovery.design?.controlType || "placebo",
      randomizationRatio: store.discovery.design?.randomizationRatio || "1:1",
    };

    queryInsights.mutate({
      sessionId: store.sessionId,
      field,
      context,
      query,
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    // Handle sample size suggestions
    if (field === "sample-size") {
      // Use actionableData if available (from JSON response)
      if (actionableData && actionableData.field === 'targetEnrollment') {
        setFormData(prev => ({ ...prev, targetEnrollment: actionableData.value }));
        toast.success("Sample size applied");
      } else {
        // Fallback to regex parsing
        const numberMatch = suggestion.match(/(\d+)\s*(?:patients?|participants?|subjects?)?/i);
        if (numberMatch) {
          setFormData(prev => ({ ...prev, targetEnrollment: numberMatch[1] }));
          toast.success("Sample size applied");
        }
      }
      return;
    }
    
    // Handle demographics suggestions
    if (field === "demographics") {
      if (actionableData) {
        const updates: any = {};
        
        if (actionableData.field === 'ageRange') {
          updates.ageMin = parseInt(actionableData.min);
          updates.ageMax = parseInt(actionableData.max);
          toast.success("Age range updated");
        } else if (actionableData.field === 'gender') {
          updates.gender = actionableData.value;
          toast.success("Gender requirement updated");
        } else if (actionableData.field === 'healthyVolunteers') {
          updates.healthyVolunteers = actionableData.value;
          toast.success("Healthy volunteers setting updated");
        } else if (actionableData.field === 'specificPopulation') {
          updates.specificPopulation = actionableData.value;
          toast.success("Specific population updated");
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    // Handle inclusion criteria suggestions with structured data
    if (field === "inclusion-criteria") {
      if (actionableData) {
        if (actionableData.field === 'bulkInclusionCriteria' && actionableData.criteria) {
          // Filter out duplicates
          const existingCriteria = new Set(formData.inclusionCriteria.map(c => c.toLowerCase()));
          const newCriteria = actionableData.criteria.filter(
            (c: string) => !existingCriteria.has(c.toLowerCase())
          );
          
          if (newCriteria.length > 0) {
            setFormData({
              ...formData,
              inclusionCriteria: [...formData.inclusionCriteria, ...newCriteria],
            });
            toast.success(`Added ${newCriteria.length} inclusion criteria`);
          } else {
            toast.info("All suggested criteria are already included");
          }
        }
      } else {
        // Fallback to parsing text
        const lines = suggestion.split('\n').filter(line => line.trim());
        const newCriteria: string[] = [];
        
        lines.forEach(line => {
          const match = line.match(/^[\u2022\-\d+\.]\s*(.+)/);
          if (match) {
            newCriteria.push(match[1].trim());
          }
        });
        
        if (newCriteria.length > 0) {
          const existingCriteria = new Set(formData.inclusionCriteria.map(c => c.toLowerCase()));
          const uniqueCriteria = newCriteria.filter(c => !existingCriteria.has(c.toLowerCase()));
          
          if (uniqueCriteria.length > 0) {
            setFormData({
              ...formData,
              inclusionCriteria: [...formData.inclusionCriteria, ...uniqueCriteria],
            });
            toast.success(`Added ${uniqueCriteria.length} inclusion criteria`);
          }
        }
      }
      return;
    }
    
    // Handle exclusion criteria suggestions with structured data
    if (field === "exclusion-criteria") {
      if (actionableData) {
        if (actionableData.field === 'bulkExclusionCriteria' && actionableData.criteria) {
          // Filter out duplicates
          const existingCriteria = new Set(formData.exclusionCriteria.map(c => c.toLowerCase()));
          const newCriteria = actionableData.criteria.filter(
            (c: string) => !existingCriteria.has(c.toLowerCase())
          );
          
          if (newCriteria.length > 0) {
            setFormData({
              ...formData,
              exclusionCriteria: [...formData.exclusionCriteria, ...newCriteria],
            });
            toast.success(`Added ${newCriteria.length} exclusion criteria`);
          } else {
            toast.info("All suggested criteria are already included");
          }
        }
      } else {
        // Fallback to parsing text
        const lines = suggestion.split('\n').filter(line => line.trim());
        const newCriteria: string[] = [];
        
        lines.forEach(line => {
          const match = line.match(/^[\u2022\-\d+\.]\s*(.+)/);
          if (match) {
            newCriteria.push(match[1].trim());
          }
        });
        
        if (newCriteria.length > 0) {
          const existingCriteria = new Set(formData.exclusionCriteria.map(c => c.toLowerCase()));
          const uniqueCriteria = newCriteria.filter(c => !existingCriteria.has(c.toLowerCase()));
          
          if (uniqueCriteria.length > 0) {
            setFormData({
              ...formData,
              exclusionCriteria: [...formData.exclusionCriteria, ...uniqueCriteria],
            });
            toast.success(`Added ${uniqueCriteria.length} exclusion criteria`);
          }
        }
      }
      return;
    }
    
    // Fallback for other types - parse the suggestion for criteria items
    const lines = suggestion.split('\n').filter(line => line.trim());
    const newCriteria: string[] = [];
    
    lines.forEach(line => {
      // Look for bullet points or numbered items
      const match = line.match(/^[\u2022\-\d+\.]\s*(.+)/);
      if (match) {
        newCriteria.push(match[1].trim());
      } else if (line.trim() && !line.includes(':') && line.length > 10) {
        // Also include lines that look like criteria even without bullets
        newCriteria.push(line.trim());
      }
    });

    if (newCriteria.length > 0) {
      // Determine if these are inclusion or exclusion criteria
      const isExclusion = field.includes('exclusion') || 
                          suggestion.toLowerCase().includes('exclusion') || 
                          suggestion.toLowerCase().includes('contraindication');
      
      if (isExclusion) {
        setFormData({
          ...formData,
          exclusionCriteria: [...formData.exclusionCriteria, ...newCriteria],
        });
      } else {
        setFormData({
          ...formData,
          inclusionCriteria: [...formData.inclusionCriteria, ...newCriteria],
        });
      }
      
      toast.success(`Added ${newCriteria.length} criteria`);
    }
    
    // Don't close panel for criteria additions
    return;
  };

  const handleViewAllSources = () => {
    if (activeInsightsPanel && (insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources)) {
      const sources = insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || [];
      setSourceStudiesViewerSources(sources);
      setSourceStudiesViewerOpen(true);
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Define your target population
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Specify the patient population for your clinical trial
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Target Enrollment
                </CardTitle>
                <CardDescription>
                  Expected number of participants to enroll
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("sample-size")}
                onRefresh={() => handleGetInsights("sample-size", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "sample-size"}
                hasCachedData={!!cachedInsights["sample-size"]}
                showRefresh={!!cachedInsights["sample-size"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="targetEnrollment">Number of Participants</Label>
                <Input
                  id="targetEnrollment"
                  placeholder="e.g., 100, 250, 500"
                  value={formData.targetEnrollment}
                  onChange={(e) => setFormData({ ...formData, targetEnrollment: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="geographicScope">Geographic Scope</Label>
                <RadioGroup
                  value={formData.geographicScope}
                  onValueChange={(value) => setFormData({ ...formData, geographicScope: value as any })}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="local" id="geo-local" />
                    <Label htmlFor="geo-local" className="font-normal text-sm">Local/Regional</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="national" id="geo-national" />
                    <Label htmlFor="geo-national" className="font-normal text-sm">National</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="international" id="geo-international" />
                    <Label htmlFor="geo-international" className="font-normal text-sm">International</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Demographics
                </CardTitle>
                <CardDescription>
                  Basic demographic requirements for enrollment
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("demographics")}
                onRefresh={() => handleGetInsights("demographics", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "demographics"}
                hasCachedData={!!cachedInsights["demographics"]}
                showRefresh={!!cachedInsights["demographics"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ageMin">Minimum Age</Label>
                <Input
                  id="ageMin"
                  type="number"
                  min="0"
                  max="120"
                  value={formData.ageMin}
                  onChange={(e) => setFormData({ ...formData, ageMin: parseInt(e.target.value) || 0 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ageMax">Maximum Age</Label>
                <Input
                  id="ageMax"
                  type="number"
                  min="0"
                  max="120"
                  value={formData.ageMax}
                  onChange={(e) => setFormData({ ...formData, ageMax: parseInt(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label>Gender</Label>
              <RadioGroup
                value={formData.gender}
                onValueChange={(value) => setFormData({ ...formData, gender: value as Gender })}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="gender-all" />
                  <Label htmlFor="gender-all" className="font-normal">
                    All genders
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="male" id="gender-male" />
                  <Label htmlFor="gender-male" className="font-normal">
                    Male only
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="female" id="gender-female" />
                  <Label htmlFor="gender-female" className="font-normal">
                    Female only
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="specificPopulation">Specific Population (Optional)</Label>
                <Input
                  id="specificPopulation"
                  placeholder="e.g., Elderly, Pediatric, Pregnant"
                  value={formData.specificPopulation}
                  onChange={(e) => setFormData({ ...formData, specificPopulation: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label>Healthy Volunteers</Label>
                <RadioGroup
                  value={formData.healthyVolunteers ? "yes" : "no"}
                  onValueChange={(value) => setFormData({ ...formData, healthyVolunteers: value === "yes" })}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yes" id="healthy-yes" />
                    <Label htmlFor="healthy-yes" className="font-normal text-sm">Accepts Healthy Volunteers</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="no" id="healthy-no" />
                    <Label htmlFor="healthy-no" className="font-normal text-sm">Patients Only</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Inclusion Criteria</CardTitle>
              <InsightsButton
                onClick={() => handleGetInsights('inclusion-criteria')}
                onRefresh={() => handleGetInsights('inclusion-criteria', true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === 'inclusion-criteria'}
                hasCachedData={!!cachedInsights['inclusion-criteria']}
                showRefresh={!!cachedInsights['inclusion-criteria']}
              />
            </div>
            <CardDescription>
              Key requirements patients must meet to participate
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.inclusionCriteria.map((criteria, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                <span className="flex-1 text-sm">{criteria}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeInclusionCriteria(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add inclusion criteria..."
                value={newInclusion}
                onChange={(e) => setNewInclusion(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addInclusionCriteria())}
              />
              <Button onClick={addInclusionCriteria} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Exclusion Criteria</CardTitle>
              <InsightsButton
                onClick={() => handleGetInsights('exclusion-criteria')}
                onRefresh={() => handleGetInsights('exclusion-criteria', true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === 'exclusion-criteria'}
                hasCachedData={!!cachedInsights['exclusion-criteria']}
                showRefresh={!!cachedInsights['exclusion-criteria']}
              />
            </div>
            <CardDescription>
              Conditions that would disqualify patients from participating
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.exclusionCriteria.map((criteria, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                <span className="flex-1 text-sm">{criteria}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeExclusionCriteria(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add exclusion criteria..."
                value={newExclusion}
                onChange={(e) => setNewExclusion(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addExclusionCriteria())}
              />
              <Button onClick={addExclusionCriteria} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.5} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/study-design")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Timeline"}
          </Button>
        </div>
      </BlurFade>

      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={getInsightsPanelTitle(activeInsightsPanel)}
          description="Recommendations based on similar studies in our knowledge base"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onViewAllSources={handleViewAllSources}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}
      
      <DocumentViewerPortal
        isOpen={!!documentViewerUrl}
        onClose={() => setDocumentViewerUrl(null)}
        documentUrl={documentViewerUrl}
        loading={false}
      />
      
      <SourceStudiesViewerPortal
        isOpen={sourceStudiesViewerOpen}
        onClose={() => setSourceStudiesViewerOpen(false)}
        sources={sourceStudiesViewerSources}
        onDocumentClick={(url) => setDocumentViewerUrl(url)}
      />
    </div>
  );
}