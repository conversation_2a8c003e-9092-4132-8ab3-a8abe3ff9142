import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { generateMockStudies, generateMockInsights } from "~/services/mock/trial-data";
import type { StudyDesignSession } from "~/types/trial-design";

// In-memory session storage for development
const sessions = new Map<string, StudyDesignSession>();

export const studyDesignRouter = createTRPCRouter({
  createSession: publicProcedure
    .input(z.object({
      userId: z.string().optional(),
    }).optional())
    .mutation(async ({ input, ctx }) => {
      const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const session: StudyDesignSession = {
        id: sessionId,
        userId: input?.userId ?? ctx.session?.user?.id,
        status: "discovery",
        currentStep: "study-type",
        completedSteps: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        discovery: {
          studyType: "drug" as const,
          intervention: {},
          condition: "",
          population: {
            gender: "all",
          },
          objectives: {
            primaryGoal: "",
            keyOutcome: "",
          },
        },
      };
      
      sessions.set(sessionId, session);
      
      return {
        sessionId,
        expiresAt: session.expiresAt,
      };
    }),

  getSession: publicProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session is expired
      if (session.expiresAt < new Date()) {
        sessions.delete(input.sessionId);
        throw new Error("Session expired");
      }
      
      return session;
    }),

  saveDiscovery: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      data: z.object({
        studyType: z.enum(["drug", "device", "behavioral", "diagnostic", "other"]).optional(),
        phase: z.enum(["phase1", "phase2", "phase3", "phase4", "unknown"]).optional(),
        intervention: z.object({
          name: z.string().optional(),
          category: z.string().optional(),
          mechanism: z.string().optional(),
          class: z.string().optional(),
          isNewCompound: z.boolean().optional(),
          deviceClass: z.string().optional(),
        }).optional(),
        condition: z.string().optional(),
        population: z.object({
          ageMin: z.number().optional(),
          ageMax: z.number().optional(),
          gender: z.enum(["all", "male", "female"]).optional(),
          specificPopulation: z.string().optional(),
          inclusionCriteria: z.array(z.string()).optional(),
          exclusionCriteria: z.array(z.string()).optional(),
        }).optional(),
        objectives: z.object({
          primaryGoal: z.string().optional(),
          keyOutcome: z.string().optional(),
          secondaryGoals: z.array(z.string()).optional(),
        }).optional(),
      }),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Update discovery data - merge nested objects properly
      if (input.data.intervention) {
        session.discovery.intervention = {
          ...session.discovery.intervention,
          ...input.data.intervention,
        };
      }
      
      if (input.data.population) {
        session.discovery.population = {
          ...session.discovery.population,
          ...input.data.population,
        };
      }
      
      if (input.data.objectives) {
        session.discovery.objectives = {
          ...session.discovery.objectives,
          ...input.data.objectives,
        };
      }
      
      // Update top-level fields
      if (input.data.studyType) {
        session.discovery.studyType = input.data.studyType;
      }
      
      if (input.data.phase) {
        session.discovery.phase = input.data.phase;
      }
      
      if (input.data.condition) {
        session.discovery.condition = input.data.condition;
      }
      
      session.updatedAt = new Date();
      sessions.set(input.sessionId, session);
      
      return {
        success: true,
        sessionId: input.sessionId,
      };
    }),

  updateStep: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      currentStep: z.string(),
      completedStep: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      session.currentStep = input.currentStep;
      
      if (input.completedStep && !session.completedSteps.includes(input.completedStep)) {
        session.completedSteps.push(input.completedStep);
      }
      
      session.updatedAt = new Date();
      sessions.set(input.sessionId, session);
      
      return {
        success: true,
        currentStep: session.currentStep,
        completedSteps: session.completedSteps,
      };
    }),

  listSessions: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session?.user?.id;
      if (!userId) return [];
      
      const userSessions = Array.from(sessions.values())
        .filter(s => s.userId === userId && s.expiresAt > new Date())
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
      
      return userSessions;
    }),

  deleteSession: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if user owns this session
      if (session.userId !== ctx.session?.user?.id) {
        throw new Error("Unauthorized");
      }
      
      sessions.delete(input.sessionId);
      
      return {
        success: true,
        deletedSessionId: input.sessionId,
      };
    }),

  searchKnowledgeBase: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      discovery: z.any(), // Discovery data to search with
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // In production, this would call AWS Lambda/Bedrock
      // For now, use mock data
      const studies = generateMockStudies(15);
      
      // Store results in session
      session.knowledgeBaseResults = {
        studies,
        selectedStudies: [],
        queryMetadata: {
          searchTime: Date.now(),
          totalResults: studies.length,
        },
      };
      session.status = "analyzing";
      session.updatedAt = new Date();
      
      sessions.set(input.sessionId, session);
      
      return {
        studies,
        metadata: session.knowledgeBaseResults.queryMetadata,
      };
    }),
  
  saveSelectedStudies: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      studyIds: z.array(z.string()),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      if (!session.knowledgeBaseResults) {
        throw new Error("No search results found");
      }
      
      // Update selected studies
      session.knowledgeBaseResults.selectedStudies = input.studyIds;
      
      // Generate insights based on selected studies
      // In production, this would call AWS Lambda/Bedrock
      const insights = generateMockInsights(input.studyIds);
      session.insights = insights;
      
      session.status = "designing";
      session.updatedAt = new Date();
      
      sessions.set(input.sessionId, session);
      
      return {
        success: true,
        insights,
      };
    }),
  
  getInsights: publicProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      if (!session.insights) {
        // Generate mock insights if not available
        const studyIds = session.knowledgeBaseResults?.selectedStudies || [];
        session.insights = generateMockInsights(studyIds);
        sessions.set(input.sessionId, session);
      }
      
      return session.insights;
    }),

  generateSynopsis: publicProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      const discovery = session.discovery;
      const sections: Record<string, string> = {};
      
      // Generate sections progressively
      // Each section is generated independently and can be returned immediately
      
      try {
        // 1. Study Overview (uses KB for rationale)
        sections.studyOverview = await generateStudyOverviewSection(discovery);
        
        // 2. Study Design (no KB needed)
        sections.studyDesign = generateStudyDesignSection(discovery);
        
        // 3. Study Population (no KB needed)
        sections.studyPopulation = generateStudyPopulationSection(discovery);
        
        // 4. Interventions (uses KB for dosing)
        sections.interventions = await generateInterventionsSection(discovery);
        
        // 5. Endpoints (no KB needed)
        sections.endpoints = generateEndpointsSection(discovery);
        
        // 6. Procedures (no KB needed)
        sections.procedures = generateProceduresSection(discovery);
        
        // 7. Operational (uses KB for regulatory)
        sections.operational = await generateOperationalSection(discovery);
        
        // Store in session
        session.synopsis = sections;
        sessions.set(input.sessionId, session);
        
        return {
          sections,
          fullDocument: Object.values(sections).join('\n\n'),
        };
      } catch (error) {
        console.error('Error generating synopsis:', error);
        throw new Error('Failed to generate synopsis');
      }
    }),
});

// Section Generator Functions
function generateStudyTitle(discovery: any): string {
  const phase = discovery.phase || 'Phase 2';
  const design = discovery.design?.designType || 'Randomized';
  const intervention = discovery.intervention?.name || 'Investigational Drug';
  const condition = discovery.condition || 'Target Condition';
  
  return `${phase} ${design.charAt(0).toUpperCase() + design.slice(1)} Study of ${intervention} in ${condition}`;
}

async function generateStudyOverviewSection(discovery: any): Promise<string> {
  const title = generateStudyTitle(discovery);
  const phase = discovery.phase || 'Phase 2';
  const intervention = discovery.intervention?.name || 'the investigational drug';
  const condition = discovery.condition || 'the target condition';
  const primaryEndpoint = discovery.objectives?.primaryGoal || 'efficacy and safety';
  const enrollment = discovery.population?.targetEnrollment || 'targeted number of';
  
  // In a real implementation, this would query the KB for rationale
  // For now, generate based on collected data
  const overview = `# STUDY OVERVIEW

**${title}**

This ${phase} clinical study is designed to evaluate the ${primaryEndpoint} of ${intervention} in patients with ${condition}. The study will enroll ${enrollment} participants across multiple clinical sites. ${intervention} represents a ${discovery.intervention?.drugClass || 'novel therapeutic'} approach to treating ${condition}, addressing the significant unmet medical need in this patient population.

The primary objective is to ${discovery.objectives?.primaryGoal || 'assess the efficacy and safety'} of ${intervention} compared to ${discovery.design?.controlType || 'placebo'} in ${condition} patients. This ${discovery.design?.designType || 'randomized controlled'} trial will provide critical data to support the continued development of ${intervention} in this therapeutic area.`;
  
  return overview;
}

function generateStudyDesignSection(discovery: any): string {
  const design = discovery.design?.designType || 'parallel-group';
  const control = discovery.design?.controlType || 'placebo';
  const randomization = discovery.design?.randomizationRatio || '1:1';
  const blinding = discovery.design?.blinding || 'double-blind';
  const treatmentDuration = discovery.timeline?.treatmentPeriod || '12 weeks';
  const followUp = discovery.timeline?.followUpPeriod || '4 weeks';
  
  return `# STUDY DESIGN AND METHODOLOGY

This is a ${blinding}, ${control}-controlled, ${design} study with ${randomization} randomization. Participants will be randomly assigned to receive either ${discovery.intervention?.name || 'the investigational drug'} or ${control} for a treatment period of ${treatmentDuration}.

The study consists of a ${discovery.timeline?.screeningPeriod || '2-week'} screening period, a ${discovery.timeline?.baselinePeriod || '1-week'} baseline assessment period, the ${treatmentDuration} treatment period, and a ${followUp} safety follow-up period. The total duration of study participation for each subject will be approximately ${discovery.timeline?.totalDuration || '18 weeks'}.

Randomization will be stratified by key baseline characteristics to ensure balanced treatment groups. The ${blinding} design will be maintained throughout the study period to minimize bias in outcome assessments.`;
}

function generateStudyPopulationSection(discovery: any): string {
  const ageMin = discovery.population?.ageMin || 18;
  const ageMax = discovery.population?.ageMax || 65;
  const enrollment = discovery.population?.targetEnrollment || '100';
  const gender = discovery.population?.gender === 'all' ? 'all genders' : discovery.population?.gender || 'all genders';
  
  // Convert inclusion criteria array to prose
  const inclusionText = discovery.population?.inclusionCriteria?.length > 0
    ? discovery.population.inclusionCriteria.join('; ')
    : 'meeting standard diagnostic criteria for the condition';
    
  // Convert exclusion criteria array to prose
  const exclusionText = discovery.population?.exclusionCriteria?.length > 0
    ? discovery.population.exclusionCriteria.join('; ')
    : 'significant comorbidities or contraindications';
  
  return `# STUDY POPULATION

The study will enroll ${enrollment} participants aged ${ageMin} to ${ageMax} years, including ${gender}. Eligible participants must have ${inclusionText}. ${discovery.population?.specificPopulation ? `The study specifically targets ${discovery.population.specificPopulation}.` : ''}

Key exclusion criteria include ${exclusionText}. ${discovery.population?.healthyVolunteers ? 'Healthy volunteers will be accepted for this study.' : 'Only patients with the target condition will be enrolled.'}

The study population will be recruited from ${discovery.operational?.numberOfSites || 'multiple'} sites across ${discovery.population?.geographicScope === 'international' ? 'multiple countries' : discovery.population?.geographicScope || 'the region'}, ensuring a diverse and representative sample.`;
}

async function generateInterventionsSection(discovery: any): Promise<string> {
  const intervention = discovery.intervention?.name || 'Investigational Drug';
  const drugClass = discovery.intervention?.drugClass || 'therapeutic agent';
  const control = discovery.design?.controlType || 'placebo';
  const duration = discovery.timeline?.treatmentPeriod || '12 weeks';
  
  // In a real implementation, this would query KB for dosing
  // For now, use placeholder text
  return `# INTERVENTIONS AND TREATMENTS

Participants will receive either ${intervention}, a ${drugClass}, or ${control} for ${duration}. ${intervention} will be administered according to the protocol-specified dosing regimen, with dose adjustments permitted based on tolerability and safety assessments.

The ${control} will be identical in appearance to ensure maintenance of the blind. No rescue medications or concomitant treatments for the primary condition will be permitted during the treatment period, except as specified in the protocol for safety reasons.

Treatment compliance will be assessed at each study visit through drug accountability and participant diary review. Participants must maintain at least 80% compliance to remain in the per-protocol analysis population.`;
}

function generateEndpointsSection(discovery: any): string {
  const primaryEndpoint = discovery.objectives?.primaryGoal || 'change from baseline in disease severity';
  const keyMeasure = discovery.objectives?.keyOutcome || 'validated assessment scale';
  const secondaryEndpoints = discovery.objectives?.secondaryGoals?.join('; ') || 'safety and tolerability measures';
  
  return `# STUDY ENDPOINTS AND OUTCOMES

**Primary Endpoint:**
The primary efficacy endpoint is ${primaryEndpoint}, measured using ${keyMeasure}. This will be assessed at the end of the treatment period compared to baseline.

**Secondary Endpoints:**
Secondary endpoints include ${secondaryEndpoints}. Additional endpoints include quality of life assessments, healthcare resource utilization, and patient-reported outcomes.

**Safety Endpoints:**
Safety will be evaluated through the incidence and severity of treatment-emergent adverse events (TEAEs), changes in vital signs, laboratory parameters, and electrocardiogram findings. All safety assessments will be conducted according to standard clinical practice guidelines.`;
}

function generateProceduresSection(discovery: any): string {
  const visits = discovery.timeline?.visits || [];
  const visitCount = visits.length || 6;
  const criticalVisits = visits.filter((v: any) => v.critical).map((v: any) => v.name).join(', ') || 'screening, baseline, and end of treatment';
  
  return `# STUDY PROCEDURES AND ASSESSMENTS

The study includes ${visitCount} scheduled visits over the course of participation. Critical visits include ${criticalVisits}, where primary endpoint assessments will be conducted.

**Screening procedures** include informed consent, medical history, physical examination, laboratory assessments, and confirmation of eligibility criteria.

**Treatment period assessments** will be conducted at regular intervals and include efficacy measurements, safety monitoring, adverse event collection, and medication compliance checks.

**End of study procedures** include final efficacy assessments, safety evaluations, and arrangements for post-study care if applicable. Participants will be followed for safety for ${discovery.timeline?.followUpPeriod || '4 weeks'} after the last dose of study medication.`;
}

async function generateOperationalSection(discovery: any): Promise<string> {
  const sites = discovery.operational?.numberOfSites || 'multiple';
  const recruitmentRate = discovery.operational?.recruitmentRate || '2-3 patients/site/month';
  const dataManagement = discovery.operational?.dataManagement === 'edc' ? 'electronic data capture (EDC)' : 
                         discovery.operational?.dataManagement || 'electronic data capture';
  const monitoring = discovery.operational?.monitoringApproach || 'risk-based monitoring';
  
  // In a real implementation, would query KB for regulatory requirements
  return `# OPERATIONAL AND REGULATORY CONSIDERATIONS

The study will be conducted at ${sites} clinical sites with an expected recruitment rate of ${recruitmentRate}. Screen failure rate is estimated at ${discovery.operational?.screenFailureRate || '20%'} with an expected dropout rate of ${discovery.operational?.dropoutRate || '15%'}.

Data will be collected using ${dataManagement} with ${monitoring} approach to ensure data quality and participant safety. Regular monitoring visits will be conducted according to the monitoring plan, with increased frequency for sites with higher enrollment or quality concerns.

The study will be conducted in accordance with Good Clinical Practice (GCP) guidelines, the Declaration of Helsinki, and all applicable regulatory requirements. Ethics committee approval will be obtained before study initiation at each site, and all participants will provide written informed consent before any study procedures are performed.

Safety oversight will be provided by the study team with regular review of safety data. Stopping rules and discontinuation criteria are defined in the protocol to ensure participant safety throughout the study.`;
}