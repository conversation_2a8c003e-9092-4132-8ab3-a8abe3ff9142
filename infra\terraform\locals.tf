locals {
  project_name = "trialynx-insights"
  
  common_tags = {
    Project     = "TriaLynx-Insights"
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
  
  lambda_function_name = "${local.project_name}-${var.environment}-query-kb"
  api_gateway_name     = "${local.project_name}-${var.environment}-api"
  
  lambda_environment = {
    AWS_REGION                = var.aws_region
    BEDROCK_KNOWLEDGE_BASE_ID = var.bedrock_knowledge_base_id
    BEDROCK_MODEL_ID          = var.bedrock_model_id
    CORS_ORIGIN               = join(",", var.cors_allowed_origins)
    JWT_SECRET_ARN            = "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:${var.jwt_secret_name}"
  }
}