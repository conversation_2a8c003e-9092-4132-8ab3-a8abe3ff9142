import { studyDesignRouter } from "~/server/api/routers/studyDesign";
import { knowledgeBaseRouter } from "~/server/api/routers/knowledgeBase";
import { insightsRouter } from "~/server/api/routers/insights";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  studyDesign: studyDesignRouter,
  knowledgeBase: knowledgeBaseRouter,
  insights: insightsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
