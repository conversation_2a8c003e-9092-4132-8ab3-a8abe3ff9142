"use client";

import { useState, useEffect } from "react";
import { X, Download, ExternalLink, FileText, Loader2, ChevronRight, ChevronDown, Calendar, Users, MapPin, Building, TestTube, Target, ClipboardList, Activity } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Skeleton } from "~/components/ui/skeleton";
import { cn } from "~/lib/utils";
import { api } from "~/trpc/react";

interface DocumentSection {
  title: string;
  content: string | string[];
  subsections?: DocumentSection[];
}

interface DocumentData {
  nctId: string;
  title: string;
  officialTitle?: string;
  status?: string;
  studyType?: string;
  phase?: string;
  enrollment?: number;
  startDate?: string;
  primaryCompletionDate?: string;
  studyCompletionDate?: string;
  sponsor?: string;
  sponsorType?: string;
  description?: string;
  conditions?: string[];
  keywords?: string[];
  primaryOutcomes?: Array<{
    name: string;
    description?: string;
    timeFrame?: string;
  }>;
  secondaryOutcomes?: Array<{
    name: string;
    description?: string;
    timeFrame?: string;
  }>;
  eligibility?: {
    inclusionCriteria?: string[];
    exclusionCriteria?: string[];
    gender?: string;
    ageRange?: string;
    healthyVolunteers?: boolean;
  };
  locations?: Array<{
    name: string;
    city?: string;
    state?: string;
    country?: string;
    status?: string;
  }>;
  armsAndGroups?: Array<{
    name: string;
    type: string;
    description: string;
  }>;
  sections?: DocumentSection[];
  rawContent?: string;
}

interface DocumentViewerProps {
  isOpen: boolean;
  onClose: () => void;
  documentUrl?: string;
  documentData?: DocumentData;
  loading?: boolean;
  error?: string;
}

export function DocumentViewer({
  isOpen,
  onClose,
  documentUrl,
  documentData,
  loading: propLoading = false,
  error: propError
}: DocumentViewerProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [parsedData, setParsedData] = useState<DocumentData | null>(null);
  
  // Use TRPC to fetch document from S3
  const { data: fetchedData, isLoading: isFetching, error: fetchError } = api.knowledgeBase.fetchDocument.useQuery(
    { s3Uri: documentUrl || '' },
    { 
      enabled: !!documentUrl && isOpen && !documentData,
      retry: 1,
    }
  );
  
  const loading = propLoading || isFetching;
  const error = propError || (fetchError ? 'Failed to load document. Please try again.' : null);

  useEffect(() => {
    if (documentData) {
      setParsedData(documentData);
    } else if (fetchedData) {
      setParsedData({
        nctId: fetchedData.nctId,
        title: fetchedData.parsed.title || fetchedData.title,
        officialTitle: fetchedData.parsed.officialTitle,
        status: fetchedData.parsed.status,
        studyType: fetchedData.parsed.studyType,
        phase: fetchedData.parsed.phase,
        enrollment: fetchedData.parsed.enrollment,
        startDate: fetchedData.parsed.startDate,
        primaryCompletionDate: fetchedData.parsed.primaryCompletionDate,
        studyCompletionDate: fetchedData.parsed.studyCompletionDate,
        sponsor: fetchedData.parsed.sponsor,
        sponsorType: fetchedData.parsed.sponsorType,
        conditions: fetchedData.parsed.conditions,
        keywords: fetchedData.parsed.keywords,
        primaryOutcomes: fetchedData.parsed.primaryOutcomes,
        secondaryOutcomes: fetchedData.parsed.secondaryOutcomes,
        eligibility: fetchedData.parsed.eligibility,
        locations: fetchedData.parsed.locations,
        armsAndGroups: fetchedData.parsed.armsAndGroups,
        sections: fetchedData.parsed.sections,
        rawContent: fetchedData.content,
      });
    }
  }, [documentData, fetchedData]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const renderSection = (section: DocumentSection, level: number = 0, id: string) => {
    const isExpanded = expandedSections.has(id);
    const hasSubsections = section.subsections && section.subsections.length > 0;

    return (
      <div key={id} className={cn("border-l-2 border-gray-200 dark:border-gray-700", level > 0 && "ml-4")}>
        <button
          onClick={() => hasSubsections && toggleSection(id)}
          className={cn(
            "w-full text-left p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",
            hasSubsections && "cursor-pointer"
          )}
        >
          <div className="flex items-center justify-between">
            <h3 className={cn(
              "font-semibold text-gray-900 dark:text-gray-100",
              level === 0 ? "text-lg" : "text-base"
            )}>
              {section.title}
            </h3>
            {hasSubsections && (
              isExpanded ? <ChevronDown className="h-4 w-4 text-gray-600 dark:text-gray-400" /> : <ChevronRight className="h-4 w-4 text-gray-600 dark:text-gray-400" />
            )}
          </div>
          {(!hasSubsections || isExpanded) && section.content && (
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {Array.isArray(section.content) ? (
                <ul className="list-disc list-inside space-y-1">
                  {section.content.map((item, idx) => (
                    <li key={idx}>{item}</li>
                  ))}
                </ul>
              ) : (
                <p className="whitespace-pre-wrap">{section.content}</p>
              )}
            </div>
          )}
        </button>
        {hasSubsections && isExpanded && (
          <div>
            {section.subsections!.map((subsection, idx) => 
              renderSection(subsection, level + 1, `${id}-${idx}`)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Backdrop - Higher opacity for better visibility */}
      <div
        className={cn(
          "fixed inset-0 bg-black/80 backdrop-blur-sm transition-opacity z-[200]",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />

      {/* Modal - Light glassmorphic background with better contrast */}
      <div
        className={cn(
          "fixed inset-4 md:inset-6 lg:inset-8 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-2xl transition-all duration-300 z-[201] overflow-hidden",
          isOpen ? "scale-100 opacity-100" : "scale-95 opacity-0 pointer-events-none"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header - Light background with clear separation */}
          <div className="border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                {loading ? (
                  <Skeleton className="h-8 w-3/4" />
                ) : parsedData ? (
                  <>
                    <h2 className="text-xl font-semibold line-clamp-2 text-gray-900 dark:text-gray-100">
                      {parsedData.title}
                    </h2>
                    <div className="mt-2 flex flex-wrap items-center gap-2">
                      <Badge variant="outline">
                        <FileText className="mr-1 h-3 w-3" />
                        {parsedData.nctId}
                      </Badge>
                      {parsedData.status && (
                        <Badge>{parsedData.status}</Badge>
                      )}
                      {parsedData.phase && (
                        <Badge variant="secondary">{parsedData.phase}</Badge>
                      )}
                      {parsedData.enrollment && (
                        <Badge variant="outline">{parsedData.enrollment} participants</Badge>
                      )}
                    </div>
                  </>
                ) : (
                  <h2 className="text-xl font-semibold">Document Viewer</h2>
                )}
              </div>
              <div className="ml-4 flex gap-2">
                {documentUrl && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => window.open(documentUrl, "_blank")}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Content - Better scrolling with light background */}
          <div className="flex-1 overflow-y-auto bg-white dark:bg-gray-900">
            <div className="p-6 max-w-5xl mx-auto">
              {loading && (
                <div className="space-y-4">
                  <Skeleton className="h-32 w-full" />
                  <Skeleton className="h-32 w-full" />
                  <Skeleton className="h-32 w-full" />
                </div>
              )}

              {error && (
                <div className="rounded-lg border border-destructive p-4">
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              )}

              {!loading && !error && parsedData && (
                <div className="space-y-8">
                  {/* Official Title */}
                  {parsedData.officialTitle && (
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                      <h3 className="text-lg font-semibold mb-2 flex items-center gap-2">
                        <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        Official Title
                      </h3>
                      <p className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">{parsedData.officialTitle}</p>
                    </div>
                  )}

                  {/* Study Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {parsedData.studyType && (
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <TestTube className="h-4 w-4 text-muted-foreground" />
                          <span className="text-xs font-medium text-muted-foreground">Study Type</span>
                        </div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.studyType}</p>
                      </div>
                    )}
                    
                    {parsedData.startDate && (
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-xs font-medium text-muted-foreground">Start Date</span>
                        </div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.startDate}</p>
                      </div>
                    )}
                    
                    {parsedData.primaryCompletionDate && (
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-xs font-medium text-muted-foreground">Primary Completion</span>
                        </div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.primaryCompletionDate}</p>
                      </div>
                    )}
                  </div>

                  {/* Conditions and Keywords */}
                  {(parsedData.conditions || parsedData.keywords) && (
                    <div className="space-y-4">
                      {parsedData.conditions && parsedData.conditions.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                            <Activity className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            Conditions
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            {parsedData.conditions.map((condition, idx) => (
                              <Badge key={idx} variant="secondary" className="px-3 py-1">
                                {condition}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {parsedData.keywords && parsedData.keywords.length > 0 && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-2">Keywords</h3>
                          <div className="flex flex-wrap gap-2">
                            {parsedData.keywords.map((keyword, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Eligibility */}
                  {parsedData.eligibility && (
                    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-800/50">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <ClipboardList className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        Eligibility Criteria
                      </h3>
                      
                      <div className="grid md:grid-cols-3 gap-4 mb-6">
                        {parsedData.eligibility.ageRange && (
                          <div>
                            <span className="text-sm text-muted-foreground">Age Range</span>
                            <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.eligibility.ageRange}</p>
                          </div>
                        )}
                        {parsedData.eligibility.gender && (
                          <div>
                            <span className="text-sm text-muted-foreground">Sex</span>
                            <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.eligibility.gender}</p>
                          </div>
                        )}
                        {parsedData.eligibility.healthyVolunteers !== undefined && (
                          <div>
                            <span className="text-sm text-muted-foreground">Healthy Volunteers</span>
                            <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.eligibility.healthyVolunteers ? "Accepted" : "Not Accepted"}</p>
                          </div>
                        )}
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-6">
                        {parsedData.eligibility.inclusionCriteria && parsedData.eligibility.inclusionCriteria.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-3 text-green-600 dark:text-green-400">Inclusion Criteria</h4>
                            <ol className="space-y-2 text-sm">
                              {parsedData.eligibility.inclusionCriteria.map((criteria, idx) => (
                                <li key={idx} className="flex gap-2">
                                  <span className="text-muted-foreground font-medium">{idx + 1}.</span>
                                  <span className="text-gray-700 dark:text-gray-300">{criteria}</span>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                        
                        {parsedData.eligibility.exclusionCriteria && parsedData.eligibility.exclusionCriteria.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-3 text-red-600 dark:text-red-400">Exclusion Criteria</h4>
                            <ol className="space-y-2 text-sm">
                              {parsedData.eligibility.exclusionCriteria.map((criteria, idx) => (
                                <li key={idx} className="flex gap-2">
                                  <span className="text-muted-foreground font-medium">{idx + 1}.</span>
                                  <span className="text-gray-700 dark:text-gray-300">{criteria}</span>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Outcomes */}
                  {(parsedData.primaryOutcomes || parsedData.secondaryOutcomes) && (
                    <div className="space-y-6">
                      {parsedData.primaryOutcomes && parsedData.primaryOutcomes.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                            <Target className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            Primary Outcomes
                          </h3>
                          <div className="space-y-3">
                            {parsedData.primaryOutcomes.map((outcome, idx) => (
                              <div key={idx} className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                                <div className="font-medium mb-1 text-gray-900 dark:text-gray-100">{outcome.name}</div>
                                {outcome.description && (
                                  <p className="text-sm text-muted-foreground mb-2">{outcome.description}</p>
                                )}
                                {outcome.timeFrame && (
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <Calendar className="h-3 w-3" />
                                    <span>{outcome.timeFrame}</span>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {parsedData.secondaryOutcomes && parsedData.secondaryOutcomes.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Secondary Outcomes</h3>
                          <div className="space-y-2">
                            {parsedData.secondaryOutcomes.map((outcome, idx) => (
                              <div key={idx} className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                                <div className="font-medium text-sm text-gray-900 dark:text-gray-100">{outcome.name}</div>
                                {outcome.timeFrame && (
                                  <span className="text-xs text-muted-foreground">Time Frame: {outcome.timeFrame}</span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Arms and Groups */}
                  {parsedData.armsAndGroups && parsedData.armsAndGroups.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        Study Arms & Groups
                      </h3>
                      <div className="space-y-3">
                        {parsedData.armsAndGroups.map((arm, idx) => (
                          <div key={idx} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-gray-900 dark:text-gray-100">{arm.name}</span>
                              <Badge variant="outline">{arm.type}</Badge>
                            </div>
                            {arm.description && (
                              <p className="text-sm text-muted-foreground">{arm.description}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Locations */}
                  {parsedData.locations && parsedData.locations.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        Study Locations
                      </h3>
                      <div className="grid gap-3">
                        {parsedData.locations.map((location, idx) => (
                          <div key={idx} className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{location.name}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Sponsor */}
                  {parsedData.sponsor && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                        <Building className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        Sponsor Information
                      </h3>
                      <div className="space-y-2">
                        <div>
                          <span className="text-sm text-muted-foreground">Lead Sponsor</span>
                          <p className="font-medium text-gray-900 dark:text-gray-100">{parsedData.sponsor}</p>
                        </div>
                        {parsedData.sponsorType && (
                          <div>
                            <span className="text-sm text-muted-foreground">Type</span>
                            <p className="text-sm text-gray-700 dark:text-gray-300">{parsedData.sponsorType}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Additional Sections - Removed to avoid duplication with the formatted content above */}
                  {/* TODO: When real S3 data is available, consider if any additional sections 
                       not covered above should be displayed here */}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}