"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Checkbox } from "~/components/ui/checkbox";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Shield,
  DollarSign,
  Building2,
  Users,
  UserCheck,
  FileText,
  Gavel,
  CheckCircle,
  Plus,
  X
} from "lucide-react";

export default function RegulatoryFinancialLegalPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    // IRB & Ethics Committee (1 question)
    irbName: store.discovery.regulatory?.irbName || "",
    
    // Sponsor & Manufacturers (3 questions)
    sponsorName: store.discovery.regulatory?.sponsorName || "",
    drugManufacturerName: store.discovery.regulatory?.drugManufacturerName || "",
    deviceManufacturerName: store.discovery.regulatory?.deviceManufacturerName || "",
    
    // CRO Information (2 questions)
    willUseCRO: store.discovery.regulatory?.willUseCRO ?? false,
    croName: store.discovery.regulatory?.croName || "",
    croAddress: store.discovery.regulatory?.croAddress || "",
    croContact: store.discovery.regulatory?.croContact || "",
    
    // Data Evaluation Committees (2 questions)
    dataEvaluationCommittees: store.discovery.regulatory?.dataEvaluationCommittees || [],
    independentCommittees: store.discovery.regulatory?.independentCommittees || [],
    
    // Financial (2 questions)
    willParticipantsBeCompensated: store.discovery.regulatory?.willParticipantsBeCompensated ?? true,
    compensationDetails: store.discovery.regulatory?.compensationDetails || "",
    billingScenarios: store.discovery.regulatory?.billingScenarios || [],
  });

  const [newDataEvaluationCommittee, setNewDataEvaluationCommittee] = useState("");
  const [newIndependentCommittee, setNewIndependentCommittee] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined,
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("regulatory-financial-legal");
      router.push("/study/new/review-synopsis");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    setActiveInsightsPanel(field);
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Analyzing regulatory requirements...',
        progressMessages: [],
      }
    }));
    
    const progressUpdates = field === 'governance-oversight' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching regulatory frameworks...' },
      { delay: 3500, message: 'Analyzing governance requirements...' },
      { delay: 6000, message: 'Extracting oversight committees...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'financial-planning' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching compensation strategies...' },
      { delay: 3500, message: 'Analyzing billing structures...' },
      { delay: 6000, message: 'Extracting financial patterns...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching sponsor requirements...' },
      { delay: 3500, message: 'Analyzing organizational structures...' },
      { delay: 6000, message: 'Extracting regulatory precedents...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      geographicScope: store.discovery.population.geographicScope || undefined,
    };

    const queries: Record<string, string> = {
      "governance-oversight": `What are typical governance and oversight requirements for ${store.discovery.phase || "Phase 2/3"} trials in ${store.discovery.condition || "clinical"} studies?`,
      "sponsors-manufacturers": `What are sponsor and manufacturer requirements for ${store.discovery.condition || "clinical"} trials?`,
      "financial-planning": `What are typical participant compensation and billing strategies for ${store.discovery.phase || "Phase 2/3"} trials?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  // Array management functions
  const addDataEvaluationCommittee = () => {
    if (newDataEvaluationCommittee.trim()) {
      setFormData(prev => ({
        ...prev,
        dataEvaluationCommittees: [...prev.dataEvaluationCommittees, newDataEvaluationCommittee.trim()]
      }));
      setNewDataEvaluationCommittee("");
    }
  };

  const removeDataEvaluationCommittee = (index: number) => {
    setFormData(prev => ({
      ...prev,
      dataEvaluationCommittees: prev.dataEvaluationCommittees.filter((_, i) => i !== index)
    }));
  };

  const addIndependentCommittee = () => {
    if (newIndependentCommittee.trim()) {
      setFormData(prev => ({
        ...prev,
        independentCommittees: [...prev.independentCommittees, newIndependentCommittee.trim()]
      }));
      setNewIndependentCommittee("");
    }
  };

  const removeIndependentCommittee = (index: number) => {
    setFormData(prev => ({
      ...prev,
      independentCommittees: prev.independentCommittees.filter((_, i) => i !== index)
    }));
  };

  const handleBillingScenarioChange = (scenario: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      billingScenarios: checked 
        ? [...prev.billingScenarios, scenario]
        : prev.billingScenarios.filter(s => s !== scenario)
    }));
  };

  const billingOptions = [
    "This study involves tests and procedures that are not considered standard of care. The non-standard care activities will be paid for by the study sponsor.",
    "Free drug/device provided by sponsor",
    "Standard of care tests, visits, and/or procedures will be charged to the participant/insurance as part of normal care.",
    "There are no billable procedures associated with this study."
  ];

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Validation - require key governance fields
    if (!formData.irbName) {
      toast.error("Please specify the IRB/Ethics Committee name");
      return;
    }

    if (!formData.sponsorName) {
      toast.error("Please specify the sponsor name");
      return;
    }

    if (formData.willUseCRO && !formData.croName) {
      toast.error("Please provide CRO details since you indicated a CRO will be used");
      return;
    }

    if (formData.willParticipantsBeCompensated && !formData.compensationDetails) {
      toast.error("Please provide compensation details since participants will be compensated");
      return;
    }

    if (formData.billingScenarios.length === 0) {
      toast.error("Please select at least one billing scenario");
      return;
    }

    // Update store with comprehensive regulatory data
    store.updateDiscovery({ 
      regulatory: {
        irbName: formData.irbName,
        sponsorName: formData.sponsorName,
        drugManufacturerName: formData.drugManufacturerName,
        deviceManufacturerName: formData.deviceManufacturerName,
        willUseCRO: formData.willUseCRO,
        croName: formData.croName,
        croAddress: formData.croAddress,
        croContact: formData.croContact,
        dataEvaluationCommittees: formData.dataEvaluationCommittees,
        independentCommittees: formData.independentCommittees,
        willParticipantsBeCompensated: formData.willParticipantsBeCompensated,
        compensationDetails: formData.compensationDetails,
        billingScenarios: formData.billingScenarios,
      }
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        regulatory: {
          irbName: formData.irbName,
          sponsorName: formData.sponsorName,
          drugManufacturerName: formData.drugManufacturerName,
          deviceManufacturerName: formData.deviceManufacturerName,
          willUseCRO: formData.willUseCRO,
          croName: formData.croName,
          croAddress: formData.croAddress,
          croContact: formData.croContact,
          dataEvaluationCommittees: formData.dataEvaluationCommittees,
          independentCommittees: formData.independentCommittees,
          willParticipantsBeCompensated: formData.willParticipantsBeCompensated,
          compensationDetails: formData.compensationDetails,
          billingScenarios: formData.billingScenarios,
        }
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    if (field === "governance-oversight") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'dataEvaluationCommittees' && actionableData.committees) {
          updates.dataEvaluationCommittees = [...new Set([...formData.dataEvaluationCommittees, ...actionableData.committees])];
          toast.success("Data evaluation committees updated");
        }
        if (actionableData.field === 'independentCommittees' && actionableData.committees) {
          updates.independentCommittees = [...new Set([...formData.independentCommittees, ...actionableData.committees])];
          toast.success("Independent committees updated");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "sponsors-manufacturers") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'sponsorName') {
          updates.sponsorName = actionableData.value;
          toast.success("Sponsor name updated");
        }
        if (actionableData.field === 'willUseCRO') {
          updates.willUseCRO = actionableData.value;
          toast.success("CRO usage updated");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "financial-planning") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'compensationDetails') {
          updates.compensationDetails = actionableData.value;
          toast.success("Compensation details updated");
        }
        if (actionableData.field === 'billingScenarios' && actionableData.scenarios) {
          updates.billingScenarios = [...new Set([...formData.billingScenarios, ...actionableData.scenarios])];
          toast.success("Billing scenarios updated");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Regulatory, Financial & Legal
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Define governance framework, oversight committees, and financial arrangements
          </p>
        </div>
      </BlurFade>

      {/* IRB & Ethics Committee */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  IRB & Ethics Committee
                </CardTitle>
                <CardDescription>
                  Specify the institutional review board or ethics committee overseeing the study
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("governance-oversight")}
                onRefresh={() => handleGetInsights("governance-oversight", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "governance-oversight"}
                hasCachedData={!!cachedInsights["governance-oversight"]}
                showRefresh={!!cachedInsights["governance-oversight"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="irbName">IRB/Ethics Committee Name *</Label>
              <Input
                id="irbName"
                placeholder="e.g., Advarra IRB, University IRB, To be determined"
                value={formData.irbName}
                onChange={(e) => setFormData({ ...formData, irbName: e.target.value })}
              />
              <p className="text-xs text-muted-foreground">
                If not yet determined, enter "To be determined" and complete when details are available
              </p>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Sponsors & Manufacturers */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Sponsors & Manufacturers
                </CardTitle>
                <CardDescription>
                  Identify key organizations responsible for the study and products
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("sponsors-manufacturers")}
                onRefresh={() => handleGetInsights("sponsors-manufacturers", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "sponsors-manufacturers"}
                hasCachedData={!!cachedInsights["sponsors-manufacturers"]}
                showRefresh={!!cachedInsights["sponsors-manufacturers"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="sponsorName">Study Sponsor Name *</Label>
              <Input
                id="sponsorName"
                placeholder="e.g., Acme Pharmaceuticals, Inc., University of Medicine, To be determined"
                value={formData.sponsorName}
                onChange={(e) => setFormData({ ...formData, sponsorName: e.target.value })}
              />
              <p className="text-xs text-muted-foreground">
                The organization with primary responsibility for initiating and conducting the clinical investigation
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="drugManufacturerName">Drug Manufacturer Name & Address</Label>
                <Textarea
                  id="drugManufacturerName"
                  placeholder="Same as sponsor
Acme Pharmaceuticals, Inc.
123 Main St., Anytown, USA 12345
To be determined"
                  rows={3}
                  value={formData.drugManufacturerName}
                  onChange={(e) => setFormData({ ...formData, drugManufacturerName: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deviceManufacturerName">Device Manufacturer Name & Address</Label>
                <Textarea
                  id="deviceManufacturerName"
                  placeholder="Same as sponsor
Acme Medical Devices, Inc.
123 Main St., Anytown, USA 12345
To be determined"
                  rows={3}
                  value={formData.deviceManufacturerName}
                  onChange={(e) => setFormData({ ...formData, deviceManufacturerName: e.target.value })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* CRO Management */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Clinical Research Organization (CRO)
            </CardTitle>
            <CardDescription>
              Specify if a CRO will manage trial operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="willUseCRO"
                checked={formData.willUseCRO}
                onCheckedChange={(checked) => setFormData({ ...formData, willUseCRO: checked })}
              />
              <Label htmlFor="willUseCRO" className="text-base">
                Will a CRO (Clinical Research Organization) be used?
              </Label>
            </div>

            {formData.willUseCRO && (
              <div className="space-y-4">
                <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <UserCheck className="h-4 w-4 text-blue-600" />
                    <h4 className="font-medium text-blue-900 dark:text-blue-200">CRO Partnership Active</h4>
                  </div>
                  <p className="text-sm text-blue-800 dark:text-blue-300">
                    Please provide details about the CRO that will manage trial operations.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="croName">CRO Name</Label>
                  <Input
                    id="croName"
                    placeholder="e.g., Acme Clinical Research, Inc., To be determined"
                    value={formData.croName}
                    onChange={(e) => setFormData({ ...formData, croName: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="croAddress">CRO Address</Label>
                  <Textarea
                    id="croAddress"
                    placeholder="123 Main St.
Anytown, USA 12345"
                    rows={2}
                    value={formData.croAddress}
                    onChange={(e) => setFormData({ ...formData, croAddress: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="croContact">CRO Contact Information</Label>
                  <Textarea
                    id="croContact"
                    placeholder="(123) 456-7890
<EMAIL>"
                    rows={2}
                    value={formData.croContact}
                    onChange={(e) => setFormData({ ...formData, croContact: e.target.value })}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Data Evaluation Committees */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Data Evaluation Committees
            </CardTitle>
            <CardDescription>
              Committees that will review data during the trial
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.dataEvaluationCommittees.map((committee, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border border-purple-200 bg-purple-50 dark:bg-purple-900/20 p-3">
                <CheckCircle className="h-4 w-4 text-purple-500" />
                <span className="flex-1 text-sm">{committee}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeDataEvaluationCommittee(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add data evaluation committee (e.g., Data Monitoring Committee, Safety Monitoring Committee)..."
                value={newDataEvaluationCommittee}
                onChange={(e) => setNewDataEvaluationCommittee(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addDataEvaluationCommittee())}
              />
              <Button onClick={addDataEvaluationCommittee} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="rounded-lg bg-gray-50 dark:bg-gray-900/20 p-3">
              <p className="text-xs text-muted-foreground">
                <strong>Common committees:</strong> Data Monitoring Committee (DMC), Dose Escalation Committee (DEC), 
                Endpoint Adjudication Committee (EAC), Safety Monitoring Committee (SMC), Efficacy Monitoring Committee, 
                Trial Steering Committee (TSC)
              </p>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Independent Committees */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gavel className="h-5 w-5" />
              Independent Committees
            </CardTitle>
            <CardDescription>
              Independent committees involved in the trial (including IRB/EC)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.independentCommittees.map((committee, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 dark:bg-green-900/20 p-3">
                <Shield className="h-4 w-4 text-green-500" />
                <span className="flex-1 text-sm">{committee}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeIndependentCommittee(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add independent committee (e.g., University IRB, Ethics Review Board)..."
                value={newIndependentCommittee}
                onChange={(e) => setNewIndependentCommittee(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addIndependentCommittee())}
              />
              <Button onClick={addIndependentCommittee} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            <p className="text-xs text-muted-foreground">
              Include the IRB/EC specified above and any other independent oversight committees
            </p>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Financial Arrangements */}
      <BlurFade delay={0.7} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Financial Arrangements
                </CardTitle>
                <CardDescription>
                  Define participant compensation and study costs
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("financial-planning")}
                onRefresh={() => handleGetInsights("financial-planning", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "financial-planning"}
                hasCachedData={!!cachedInsights["financial-planning"]}
                showRefresh={!!cachedInsights["financial-planning"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Participant Compensation */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="willParticipantsBeCompensated"
                  checked={formData.willParticipantsBeCompensated}
                  onCheckedChange={(checked) => setFormData({ ...formData, willParticipantsBeCompensated: checked })}
                />
                <Label htmlFor="willParticipantsBeCompensated" className="text-base">
                  Will participants be paid/compensated to participate?
                </Label>
              </div>

              {formData.willParticipantsBeCompensated && (
                <div className="space-y-2">
                  <Label htmlFor="compensationDetails">Participant Compensation Details</Label>
                  <Textarea
                    id="compensationDetails"
                    placeholder="Reimbursement for travel costs up to $50 per visit.
Gift cards in the amount of $50 per completed visit."
                    rows={4}
                    value={formData.compensationDetails}
                    onChange={(e) => setFormData({ ...formData, compensationDetails: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Specify payment amounts, reimbursements, gift cards, and any other compensation
                  </p>
                </div>
              )}
            </div>

            {/* Billing Scenarios */}
            <div className="space-y-3">
              <Label>Who will pay for participant tests/visits/products/activities? *</Label>
              <p className="text-sm text-muted-foreground">Select all applicable billing scenarios</p>
              
              {billingOptions.map((option, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <Checkbox
                    id={`billing-${index}`}
                    checked={formData.billingScenarios.includes(option)}
                    onCheckedChange={(checked) => handleBillingScenarioChange(option, checked as boolean)}
                  />
                  <Label htmlFor={`billing-${index}`} className="text-sm leading-relaxed">
                    {option}
                  </Label>
                </div>
              ))}
              
              {formData.billingScenarios.length > 0 && (
                <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-3">
                  <p className="text-sm font-medium text-green-900 dark:text-green-200">
                    {formData.billingScenarios.length} billing scenario(s) selected
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Governance Summary */}
      <BlurFade delay={0.8} inView>
        <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <Shield className="h-5 w-5 text-gray-600" />
              <h3 className="font-medium text-gray-900">Governance Framework Summary</h3>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">IRB/Ethics</p>
                <p className="font-medium">{formData.irbName || "Not specified"}</p>
              </div>
              <div>
                <p className="text-gray-600">Sponsor</p>
                <p className="font-medium">{formData.sponsorName || "Not specified"}</p>
              </div>
              <div>
                <p className="text-gray-600">CRO Usage</p>
                <p className="font-medium">{formData.willUseCRO ? "Yes" : "No"}</p>
              </div>
              <div>
                <p className="text-gray-600">Committees</p>
                <p className="font-medium">
                  {formData.dataEvaluationCommittees.length + formData.independentCommittees.length} total
                </p>
              </div>
            </div>
            {formData.billingScenarios.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-600 mb-2">Financial Framework:</p>
                <div className="flex flex-wrap gap-1">
                  {formData.billingScenarios.map((scenario, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {scenario.split('.')[0]}...
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Navigation */}
      <BlurFade delay={0.9} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/study-procedures-operations")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Study Procedures & Operations
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Review & Synopsis"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewerPortal
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}