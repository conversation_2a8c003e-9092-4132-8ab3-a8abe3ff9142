import { APIGatewayProxyEvent } from 'aws-lambda';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

export interface AuthContext {
  userId: string;
  email: string;
  roles: string[];
}

export class AuthValidator {
  private secretsClient: SecretsManagerClient;
  private jwtSecret: string | null = null;

  constructor(region: string = process.env.AWS_REGION || 'us-east-1') {
    this.secretsClient = new SecretsManagerClient({ region });
  }

  async validateRequest(event: APIGatewayProxyEvent): Promise<AuthContext> {
    const authHeader = event.headers.Authorization || event.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7);
    
    try {
      // For MVP, we'll do basic validation
      // In production, this would validate JWT tokens properly
      const decoded = await this.validateToken(token);
      
      return {
        userId: decoded.sub || 'default-user',
        email: decoded.email || '<EMAIL>',
        roles: decoded.roles || ['researcher']
      };
    } catch (error) {
      throw new Error('Invalid authentication token');
    }
  }

  private async validateToken(token: string): Promise<any> {
    // Placeholder for JWT validation
    // In production, this would:
    // 1. Get JWT secret from Secrets Manager
    // 2. Verify token signature
    // 3. Check expiration
    // 4. Return decoded payload
    
    if (!this.jwtSecret) {
      try {
        const command = new GetSecretValueCommand({
          SecretId: process.env.JWT_SECRET_ARN || 'trialynx-jwt-secret'
        });
        const response = await this.secretsClient.send(command);
        this.jwtSecret = response.SecretString || '';
      } catch (error) {
        console.error('Failed to retrieve JWT secret:', error);
        throw new Error('Authentication configuration error');
      }
    }
    
    // Mock decoded token for now
    return {
      sub: 'user-123',
      email: '<EMAIL>',
      roles: ['researcher'],
      exp: Math.floor(Date.now() / 1000) + 3600
    };
  }
}