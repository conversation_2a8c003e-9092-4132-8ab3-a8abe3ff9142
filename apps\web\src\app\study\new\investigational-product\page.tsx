"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Beaker,
  ShieldCheck,
  FileSearch,
  Pill,
  Microscope,
  Heart,
  TestTube,
  AlertTriangle,
  Plus,
  X
} from "lucide-react";

export default function InvestigationalProductPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    // Basic drug information (existing)
    name: store.discovery.intervention.name || "",
    category: store.discovery.intervention.category || "",
    mechanism: store.discovery.intervention.mechanism || "TBD - To be determined",
    class: store.discovery.intervention.class || "TBD - To be determined",
    isNewCompound: store.discovery.intervention.isNewCompound ?? false,
    
    // Enhanced drug product information (11 critical questions)
    medicalProblem: store.discovery.intervention.medicalProblem || "",
    comparisonToExistingTreatments: store.discovery.intervention.comparisonToExistingTreatments || "TBD - To be determined",
    regulatoryStatus: store.discovery.intervention.regulatoryStatus || "TBD - To be determined",
    activeIngredients: store.discovery.intervention.activeIngredients || [],
    inactiveIngredients: store.discovery.intervention.inactiveIngredients || [],
    ingredientRationale: store.discovery.intervention.ingredientRationale || "TBD - To be determined",
    preclinicalStudies: store.discovery.intervention.preclinicalStudies || "TBD - To be determined",
    toxicityStudies: store.discovery.intervention.toxicityStudies || "TBD - To be determined",
    clinicalTrialsHistory: store.discovery.intervention.clinicalTrialsHistory || "TBD - To be determined",
    keyFindings: store.discovery.intervention.keyFindings || "TBD - To be determined",
    pregnancySafety: store.discovery.intervention.pregnancySafety || "unknown",
    pregnancySafetyDetails: store.discovery.intervention.pregnancySafetyDetails || "TBD - To be determined",
    fdaApprovalStatus: store.discovery.intervention.fdaApprovalStatus || "investigational",
    drugCompoundNumber: store.discovery.intervention.drugCompoundNumber || "TBD - To be determined",
    indApplicationNumber: store.discovery.intervention.indApplicationNumber || "TBD - To be determined",
  });

  const [newActiveIngredient, setNewActiveIngredient] = useState("");
  const [newInactiveIngredient, setNewInactiveIngredient] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined,
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("investigational-product");
      router.push("/study/new/study-design-statistics");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    setActiveInsightsPanel(field);
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Searching for drug product information...',
        progressMessages: [],
      }
    }));
    
    const progressUpdates = [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching knowledge base...' },
      { delay: 3500, message: 'Analyzing drug products...' },
      { delay: 6000, message: 'Extracting insights...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      drugName: formData.name || undefined,
      drugClass: formData.class || undefined,
      category: formData.category || undefined,
      mechanism: formData.mechanism || undefined,
      isNewCompound: formData.isNewCompound || false,
    };

    const queries: Record<string, string> = {
      "competitive-landscape": `What are existing treatments and how does ${formData.name || "this drug"} compare to current therapies for ${store.discovery.condition || "this condition"}?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  const addActiveIngredient = () => {
    if (newActiveIngredient.trim()) {
      setFormData(prev => ({
        ...prev,
        activeIngredients: [...prev.activeIngredients, newActiveIngredient.trim()]
      }));
      setNewActiveIngredient("");
    }
  };

  const removeActiveIngredient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      activeIngredients: prev.activeIngredients.filter((_, i) => i !== index)
    }));
  };

  const addInactiveIngredient = () => {
    if (newInactiveIngredient.trim()) {
      setFormData(prev => ({
        ...prev,
        inactiveIngredients: [...prev.inactiveIngredients, newInactiveIngredient.trim()]
      }));
      setNewInactiveIngredient("");
    }
  };

  const removeInactiveIngredient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      inactiveIngredients: prev.inactiveIngredients.filter((_, i) => i !== index)
    }));
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Validation - require key fields
    if (!formData.name) {
      toast.error("Please enter the drug name");
      return;
    }
    
    if (!formData.medicalProblem) {
      toast.error("Please describe the medical problem this product addresses");
      return;
    }

    // Regulatory status has a default value, so we don't need to validate it

    // Update store with comprehensive intervention data
    store.updateDiscovery({ 
      intervention: {
        // Basic information
        name: formData.name,
        category: formData.category,
        mechanism: formData.mechanism,
        class: formData.class,
        isNewCompound: formData.isNewCompound,
        
        // Enhanced drug product information
        medicalProblem: formData.medicalProblem,
        comparisonToExistingTreatments: formData.comparisonToExistingTreatments,
        regulatoryStatus: formData.regulatoryStatus,
        activeIngredients: formData.activeIngredients,
        inactiveIngredients: formData.inactiveIngredients,
        ingredientRationale: formData.ingredientRationale,
        preclinicalStudies: formData.preclinicalStudies,
        toxicityStudies: formData.toxicityStudies,
        clinicalTrialsHistory: formData.clinicalTrialsHistory,
        keyFindings: formData.keyFindings,
        pregnancySafety: formData.pregnancySafety,
        pregnancySafetyDetails: formData.pregnancySafetyDetails,
        fdaApprovalStatus: formData.fdaApprovalStatus,
        drugCompoundNumber: formData.drugCompoundNumber,
        indApplicationNumber: formData.indApplicationNumber,
      }
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        intervention: {
          name: formData.name,
          category: formData.category,
          mechanism: formData.mechanism,
          class: formData.class,
          isNewCompound: formData.isNewCompound,
          medicalProblem: formData.medicalProblem,
          comparisonToExistingTreatments: formData.comparisonToExistingTreatments,
          regulatoryStatus: formData.regulatoryStatus,
          activeIngredients: formData.activeIngredients,
          inactiveIngredients: formData.inactiveIngredients,
          ingredientRationale: formData.ingredientRationale,
          preclinicalStudies: formData.preclinicalStudies,
          toxicityStudies: formData.toxicityStudies,
          clinicalTrialsHistory: formData.clinicalTrialsHistory,
          keyFindings: formData.keyFindings,
          pregnancySafety: formData.pregnancySafety,
          pregnancySafetyDetails: formData.pregnancySafetyDetails,
          fdaApprovalStatus: formData.fdaApprovalStatus,
          drugCompoundNumber: formData.drugCompoundNumber,
          indApplicationNumber: formData.indApplicationNumber,
        }
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    if (field === "competitive-landscape") {
      if (actionableData) {
        const updates: any = {};
        
        if (actionableData.field === 'medicalProblem') {
          updates.medicalProblem = actionableData.value;
          toast.success("Medical problem description updated");
        }
        
        if (actionableData.field === 'comparisonToExistingTreatments') {
          updates.comparisonToExistingTreatments = actionableData.value;
          toast.success("Competitive analysis updated");
        }
        
        // Handle combined data with multiple insights
        if (actionableData.epidemiology && actionableData.field === 'medicalProblem') {
          // Enhance medical problem with epidemiology data
          let enhancedDescription = `${actionableData.value}\n\nDisease Burden: ${actionableData.epidemiology}`;
          if (actionableData.unmetNeed) {
            enhancedDescription += `\n\nUnmet Medical Need: ${actionableData.unmetNeed}`;
          }
          updates.medicalProblem = enhancedDescription;
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Investigational Product
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Provide comprehensive information about your investigational drug product
          </p>
        </div>
      </BlurFade>

      {/* Basic Drug Information */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Pill className="h-5 w-5" />
              Basic Drug Information
            </CardTitle>
            <CardDescription>
              Fundamental details about your investigational product
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Drug Name *</Label>
                <Input
                  id="name"
                  placeholder="e.g., ABC-123, Compound-XYZ"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="class">Drug Class</Label>
                <Input
                  id="class"
                  placeholder="e.g., GLP-1 receptor agonist, ACE inhibitor"
                  value={formData.class}
                  onChange={(e) => setFormData({ ...formData, class: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Route/Formulation</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select route/formulation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="oral">Oral</SelectItem>
                    <SelectItem value="injectable">Injectable</SelectItem>
                    <SelectItem value="topical">Topical</SelectItem>
                    <SelectItem value="inhalation">Inhalation</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="mt-4">
                <div className={`flex items-center space-x-3 p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.isNewCompound 
                    ? 'border-primary bg-primary/5 shadow-sm' 
                    : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                }`}>
                  <Switch
                    id="isNewCompound"
                    checked={formData.isNewCompound}
                    onCheckedChange={(checked) => setFormData({ ...formData, isNewCompound: checked })}
                  />
                  <div className="flex-1">
                    <Label htmlFor="isNewCompound" className="text-sm font-medium cursor-pointer">
                      Novel compound (never tested in humans)
                    </Label>
                    <p className="text-xs text-gray-600 mt-1">
                      Check if this is a completely new chemical entity
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="mechanism">Mechanism of Action</Label>
              <Textarea
                id="mechanism"
                placeholder="Describe how the drug works at the molecular/cellular level..."
                rows={3}
                value={formData.mechanism}
                onChange={(e) => setFormData({ ...formData, mechanism: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Medical Problem & Competitive Landscape */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5" />
                  Medical Problem & Treatment Landscape
                </CardTitle>
                <CardDescription>
                  Describe the medical need and how this drug compares to existing treatments
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("competitive-landscape")}
                onRefresh={() => handleGetInsights("competitive-landscape", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "competitive-landscape"}
                hasCachedData={!!cachedInsights["competitive-landscape"]}
                showRefresh={!!cachedInsights["competitive-landscape"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="medicalProblem">Medical Problem Addressed *</Label>
              <Textarea
                id="medicalProblem"
                placeholder="Describe the medical condition, unmet need, and patient impact..."
                rows={4}
                value={formData.medicalProblem}
                onChange={(e) => setFormData({ ...formData, medicalProblem: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="comparisonToExistingTreatments">Comparison to Existing Treatments</Label>
              <Textarea
                id="comparisonToExistingTreatments"
                placeholder="How does this drug compare to current standard of care and other treatments? What advantages does it offer?"
                rows={4}
                value={formData.comparisonToExistingTreatments}
                onChange={(e) => setFormData({ ...formData, comparisonToExistingTreatments: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Regulatory Status */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldCheck className="h-5 w-5" />
              Regulatory Status & Approvals
            </CardTitle>
            <CardDescription>
              Current regulatory status and approval pathways
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fdaApprovalStatus">FDA Approval Status *</Label>
                <Select
                  value={formData.fdaApprovalStatus}
                  onValueChange={(value) => setFormData({ ...formData, fdaApprovalStatus: value as any })}
                >
                  <SelectTrigger id="fdaApprovalStatus">
                    <SelectValue placeholder="Select approval status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="approved">FDA Approved for this indication</SelectItem>
                    <SelectItem value="investigational">Investigational (IND)</SelectItem>
                    <SelectItem value="compassionate">Compassionate use</SelectItem>
                    <SelectItem value="off-label">Off-label use of approved drug</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="drugCompoundNumber">Drug Compound Number</Label>
                <Input
                  id="drugCompoundNumber"
                  placeholder="e.g., ABC-123, XYZ-456"
                  value={formData.drugCompoundNumber}
                  onChange={(e) => setFormData({ ...formData, drugCompoundNumber: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="indApplicationNumber">IND Application Number</Label>
                <Input
                  id="indApplicationNumber"
                  placeholder="e.g., IND-123456"
                  value={formData.indApplicationNumber}
                  onChange={(e) => setFormData({ ...formData, indApplicationNumber: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="regulatoryStatus">Regulatory Status Details</Label>
              <Textarea
                id="regulatoryStatus"
                placeholder="Provide details about regulatory pathway, FDA interactions, orphan drug status, etc..."
                rows={3}
                value={formData.regulatoryStatus}
                onChange={(e) => setFormData({ ...formData, regulatoryStatus: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Drug Composition */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Beaker className="h-5 w-5" />
              Drug Composition
            </CardTitle>
            <CardDescription>
              Active and inactive ingredients in the investigational product
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Active Ingredients */}
            <div className="space-y-3">
              <Label>Active Ingredients</Label>
              {formData.activeIngredients.map((ingredient, index) => (
                <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                  <TestTube className="h-4 w-4 text-blue-500" />
                  <span className="flex-1 text-sm">{ingredient}</span>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeActiveIngredient(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Add active ingredient..."
                  value={newActiveIngredient}
                  onChange={(e) => setNewActiveIngredient(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addActiveIngredient())}
                />
                <Button onClick={addActiveIngredient} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Inactive Ingredients */}
            <div className="space-y-3">
              <Label>Inactive Ingredients (Excipients)</Label>
              {formData.inactiveIngredients.map((ingredient, index) => (
                <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                  <span className="flex-1 text-sm">{ingredient}</span>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeInactiveIngredient(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Add inactive ingredient..."
                  value={newInactiveIngredient}
                  onChange={(e) => setNewInactiveIngredient(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addInactiveIngredient())}
                />
                <Button onClick={addInactiveIngredient} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ingredientRationale">Formulation Rationale</Label>
              <Textarea
                id="ingredientRationale"
                placeholder="Explain the rationale for ingredient selection, formulation strategy, and any special considerations..."
                rows={3}
                value={formData.ingredientRationale}
                onChange={(e) => setFormData({ ...formData, ingredientRationale: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Preclinical & Clinical History */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Microscope className="h-5 w-5" />
              Preclinical & Clinical History
            </CardTitle>
            <CardDescription>
              Studies conducted to support the investigational product
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="preclinicalStudies">Preclinical Studies</Label>
              <Textarea
                id="preclinicalStudies"
                placeholder="Describe in vitro and in vivo studies, animal models used, efficacy findings..."
                rows={4}
                value={formData.preclinicalStudies}
                onChange={(e) => setFormData({ ...formData, preclinicalStudies: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="toxicityStudies">Toxicity Studies & Findings</Label>
              <Textarea
                id="toxicityStudies"
                placeholder="Describe toxicology studies, dose-limiting toxicities, NOAEL/NOEL, safety margins..."
                rows={4}
                value={formData.toxicityStudies}
                onChange={(e) => setFormData({ ...formData, toxicityStudies: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="clinicalTrialsHistory">Previous Clinical Trials</Label>
              <Textarea
                id="clinicalTrialsHistory"
                placeholder="List previous clinical trials, phases completed, patient populations studied..."
                rows={4}
                value={formData.clinicalTrialsHistory}
                onChange={(e) => setFormData({ ...formData, clinicalTrialsHistory: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="keyFindings">Key Safety & Efficacy Findings</Label>
              <Textarea
                id="keyFindings"
                placeholder="Summarize key safety and efficacy findings from human trials, including any significant adverse events..."
                rows={4}
                value={formData.keyFindings}
                onChange={(e) => setFormData({ ...formData, keyFindings: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Pregnancy & Special Populations */}
      <BlurFade delay={0.7} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Pregnancy & Special Population Safety
            </CardTitle>
            <CardDescription>
              Safety considerations for pregnancy and special populations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pregnancySafety">Pregnancy Safety Classification</Label>
              <Select
                value={formData.pregnancySafety}
                onValueChange={(value) => setFormData({ ...formData, pregnancySafety: value as any })}
              >
                <SelectTrigger id="pregnancySafety">
                  <SelectValue placeholder="Select pregnancy safety" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="safe">Safe for use during pregnancy</SelectItem>
                  <SelectItem value="unsafe">Unsafe/contraindicated in pregnancy</SelectItem>
                  <SelectItem value="unknown">Safety unknown</SelectItem>
                  <SelectItem value="contraindicated">Contraindicated in pregnancy</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="pregnancySafetyDetails">Pregnancy Safety Details</Label>
              <Textarea
                id="pregnancySafetyDetails"
                placeholder="Provide scientific details about reproductive risks, animal reproductive toxicity studies, pregnancy category rationale..."
                rows={4}
                value={formData.pregnancySafetyDetails}
                onChange={(e) => setFormData({ ...formData, pregnancySafetyDetails: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Navigation */}
      <BlurFade delay={0.8} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/study-overview")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Study Overview
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Study Design & Statistics"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewerPortal
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}