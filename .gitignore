# Dependencies
node_modules/
.pnp
.pnp.js
.yarn/install-state.gz

# Testing
coverage/
*.lcov
.nyc_output

# Next.js
.next/
out/
build/
*.tsbuildinfo
next-env.d.ts

# Production
dist/

# Misc
.DS_Store
*.pem
Thumbs.db

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env*.local
.env.development
.env.test
.env.production
.env.staging
!.env.example
!.env*.example

# Vercel
.vercel

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS
.DS_Store
Thumbs.db
desktop.ini

# AWS
.aws-sam/
samconfig.toml
.aws/
aws-exports.js

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
*.tfvars
!*.tfvars.example
terraform.tfplan
terraform.tfstate.backup
crash.log
crash.*.log
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.terraformrc
terraform.rc

# Lambda
apps/lambda/dist/
apps/lambda/.aws-sam/
apps/lambda/node_modules/
apps/lambda/.env*

# Build artifacts
*.zip
*.tar.gz
build/
dist/
out/

# Logs
logs/
*.log
logs.txt

# Cache
.cache/
.parcel-cache/
.turbo/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Database
*.sqlite
*.sqlite3
*.db
*.db-journal
*.db-wal

# Certificates and keys
*.pem
*.key
*.crt
*.cer
*.pfx
*.p12

# Backup files
*.backup
*.bak
*.old

# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Python (for any scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv/

# Documentation build
docs/_build/
docs/.docusaurus/
docs/node_modules/

# Storybook
storybook-static/
.storybook/out/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Turborepo
.turbo/

# Project specific
apps/web/.next/
apps/web/out/
apps/web/node_modules/
packages/*/dist/
packages/*/node_modules/

# Clinical trials data (if stored locally)
ctg-studies/
processed/
data/
*.csv
*.json
!package.json
!package-lock.json
!tsconfig.json

# Session and cache files
sessions/
.sessions/
*.session
*.cache

# Generated files
generated/
.generated/

# User-specific files
*.local
local.settings.json

# Security
.secrets/
secrets.json
credentials.json

# CI/CD
.github/actions/*/dist/
.gitlab/ci_cache/

# MCP and Claude specific
.mcp.json
.claude/
claude-flow.config.json
memory/
coordination/

# macOS
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk
