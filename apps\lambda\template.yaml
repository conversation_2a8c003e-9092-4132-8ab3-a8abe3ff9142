AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: TriaLynx Knowledge Base Lambda Functions

# Global settings for all functions
Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    Environment:
      Variables:
        BEDROCK_KNOWLEDGE_BASE_ID: !Ref BedrockKnowledgeBaseId
        BEDROCK_MODEL_ID: !Ref BedrockModelId
        S3_BUCKET_NAME: !Ref S3BucketName
        AWS_REGION: !Ref AWS::Region
  Api:
    Cors:
      AllowMethods: "'GET,POST,OPTIONS'"
      AllowHeaders: "'Content-Type,Authorization'"
      AllowOrigin: "'*'"  # In production, restrict this to your domain

# Parameters you can override
Parameters:
  BedrockKnowledgeBaseId:
    Type: String
    Description: Your Bedrock Knowledge Base ID
    Default: "M0KKCP0UI9"  # Replace with actual KB ID
  
  BedrockModelId:
    Type: String
    Description: Bedrock model to use
    Default: "us.anthropic.claude-sonnet-4-20250514-v1:0"
  
  S3BucketName:
    Type: String
    Description: S3 bucket containing clinical trials data
    Default: "trialynx-clinical-trials-gov"

Resources:
  # Main query function
  QueryKnowledgeBaseFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: query-knowledge-base.handler
      Description: Query Bedrock Knowledge Base for clinical trial insights
      MemorySize: 512
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - bedrock:RetrieveAndGenerate
                - bedrock:Retrieve
                - bedrock:InvokeModel
              Resource: '*'
            - Effect: Allow
              Action:
                - s3:GetObject
              Resource: !Sub 'arn:aws:s3:::${S3BucketName}/*'
      Events:
        QueryApi:
          Type: Api
          Properties:
            Path: /query
            Method: POST
        OptionsApi:
          Type: Api
          Properties:
            Path: /query
            Method: OPTIONS

  # JWT Authorizer (for protected endpoints)
  JwtAuthorizerFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: jwt-authorizer.handler
      Description: JWT token validation
      MemorySize: 128

  # Document retrieval function (for later)
  GetDocumentFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: dist/
      Handler: get-document.handler
      Description: Retrieve and parse S3 documents
      MemorySize: 256
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
              Resource: !Sub 'arn:aws:s3:::${S3BucketName}/*'
      Events:
        GetDocApi:
          Type: Api
          Properties:
            Path: /document/{nctId}
            Method: GET

Outputs:
  ApiUrl:
    Description: API Gateway endpoint URL
    Value: !Sub 'https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/'
  
  QueryFunctionArn:
    Description: Query Lambda Function ARN
    Value: !GetAtt QueryKnowledgeBaseFunction.Arn