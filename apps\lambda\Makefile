# Makefile for Lambda development with SAM

.PHONY: install build start test deploy clean

# Install dependencies
install:
	npm install
	sam --version || pip install aws-sam-cli

# Build TypeScript and prepare for SAM
build:
	npm run build
	sam build

# Start local API Gateway
start: build
	sam local start-api --port 3001 --warm-containers EAGER

# Start with debugging
debug: build
	sam local start-api --port 3001 --debug-port 5858 --warm-containers EAGER

# Run tests
test:
	npm test

# Deploy to AWS (dev environment)
deploy-dev: build
	sam deploy --config-env dev

# Deploy to AWS (production)
deploy-prod: build
	sam deploy --config-env prod --confirm-changeset

# Clean build artifacts
clean:
	rm -rf dist/
	rm -rf .aws-sam/
	rm -rf node_modules/

# Validate SAM template
validate:
	sam validate

# Show local endpoints
endpoints:
	@echo "Local endpoints when running 'make start':"
	@echo "  POST - http://localhost:3001/query"
	@echo "  GET  - http://localhost:3001/document/{nctId}"
	@echo ""
	@echo "Environment Variables needed in .env:"
	@echo "  BEDROCK_KNOWLEDGE_BASE_ID"
	@echo "  BEDROCK_MODEL_ID"
	@echo "  S3_BUCKET_NAME"

# Tail logs from local execution
logs:
	sam logs --tail

# Help
help:
	@echo "Available commands:"
	@echo "  make install    - Install dependencies"
	@echo "  make build      - Build TypeScript and SAM"
	@echo "  make start      - Start local API Gateway"
	@echo "  make debug      - Start with debugging enabled"
	@echo "  make test       - Run tests"
	@echo "  make deploy-dev - Deploy to dev environment"
	@echo "  make clean      - Clean build artifacts"
	@echo "  make validate   - Validate SAM template"
	@echo "  make endpoints  - Show local API endpoints"