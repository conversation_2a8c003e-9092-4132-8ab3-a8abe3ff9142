"use client";

import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { useTrialDesignStore } from "~/store/trial-design";
import { cn } from "~/lib/utils";
import { CheckCircle2, Circle, ChevronLeft, RotateCcw, <PERSON>rkles } from "lucide-react";
import { Button } from "~/components/ui/button";
import { BlurFade } from "~/components/ui/blur-fade";
import { toast } from "sonner";

const steps = [
  { id: "study-overview", label: "Study Overview & Background", path: "/study/new" },
  { id: "investigational-product", label: "Investigational Product", path: "/study/new/investigational-product" },
  { id: "study-design-statistics", label: "Study Design & Statistics", path: "/study/new/study-design-statistics" },
  { id: "study-population", label: "Study Population", path: "/study/new/study-population" },
  { id: "safety-assessment", label: "Safety Assessment", path: "/study/new/safety-assessment" },
  { id: "study-procedures-operations", label: "Study Procedures & Operations", path: "/study/new/study-procedures-operations" },
  { id: "regulatory-financial-legal", label: "Regulatory, Financial & Legal", path: "/study/new/regulatory-financial-legal" },
  { id: "review", label: "Review & Synopsis", path: "/study/new/review" },
];

export default function WizardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const store = useTrialDesignStore();
  const { completedSteps, sessionId } = store;
  
  const currentStepIndex = steps.findIndex(step => step.path === pathname);
  const currentStep = steps[currentStepIndex];
  
  const handleClearSession = () => {
    store.resetSession();
    if (typeof window !== "undefined") {
      localStorage.removeItem("trial-design-storage");
    }
    toast.success("Session cleared. Starting fresh!");
    router.push("/study/new");
  };

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Animated gradient background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-50 via-white to-teal-50">
        <div className="absolute inset-0 bg-gradient-to-tr from-[#5A32FA]/5 via-transparent to-[#00C4CC]/5" />
      </div>
      
      {/* Floating orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-[#5A32FA]/20 to-[#7D2AE8]/20 blur-3xl animate-pulse" />
        <div className="absolute top-60 -right-40 h-96 w-96 rounded-full bg-gradient-to-br from-[#00C4CC]/20 to-[#5A32FA]/20 blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-40 h-72 w-72 rounded-full bg-gradient-to-br from-[#7D2AE8]/20 to-[#00C4CC]/20 blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
      </div>

      <div className="relative z-10">
        {/* Glass-morphic header */}
        <BlurFade delay={0} inView>
          <header className="sticky top-0 z-50 border-b border-white/20 bg-white/70 backdrop-blur-xl shadow-sm">
            <nav className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push("/dashboard")}
                  className="gap-2 hover:bg-[#5A32FA]/10 transition-all"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Back to Dashboard
                </Button>
                <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent" />
                <Link href="/" className="flex items-center gap-3 group">
                  <div className="relative h-10 w-10 rounded-xl bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110">
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent" />
                    <Sparkles className="absolute inset-2 text-white" />
                  </div>
                  <span className="text-xl font-bold bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] bg-clip-text text-transparent">
                    TriaLynx Insights
                  </span>
                </Link>
              </div>
              <div className="flex items-center gap-4">
                {sessionId && (
                  <span className="text-sm text-gray-500 px-3 py-1 rounded-full bg-white/50 backdrop-blur-sm">
                    Session: {sessionId.slice(-8)}
                  </span>
                )}
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleClearSession}
                  className="border-[#5A32FA]/20 hover:border-[#5A32FA]/40 hover:bg-[#5A32FA]/5 transition-all"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Clear Session
                </Button>
              </div>
            </nav>
          </header>
        </BlurFade>

        <div className="mx-auto max-w-7xl px-6 py-8">
          {/* Progress Steps */}
          <BlurFade delay={0.1} inView>
            <div className="mb-8 bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div className="overflow-x-auto custom-scrollbar pb-3">
                <div className="flex items-center min-w-max space-x-2">
                  {steps.map((step, index) => {
                  const isCompleted = completedSteps.includes(step.id);
                  const isCurrent = index === currentStepIndex;
                  const isPast = index < currentStepIndex;
                  
                  return (
                    <div key={step.id} className="flex items-center">
                      <Link
                        href={step.path}
                        className={cn(
                          "flex items-center gap-3 rounded-xl p-3 transition-all duration-300 whitespace-nowrap",
                          isCurrent && "bg-gradient-to-r from-[#5A32FA]/10 to-[#7D2AE8]/10 shadow-md",
                          (isCompleted || isPast) && "cursor-pointer hover:bg-[#5A32FA]/5 hover:scale-105",
                          !isCompleted && !isPast && !isCurrent && "cursor-not-allowed opacity-50"
                        )}
                        onClick={(e) => {
                          if (!isCompleted && !isPast && !isCurrent) {
                            e.preventDefault();
                          }
                        }}
                      >
                        <div className="flex h-10 w-10 items-center justify-center">
                          {isCompleted || isPast ? (
                            <div className="relative">
                              <CheckCircle2 className="h-8 w-8 text-[#5A32FA]" />
                              <div 
                                className="absolute inset-0 h-8 w-8 rounded-full bg-[#5A32FA] animate-pulse-wave"
                                style={{ animationDelay: `${index * 0.4}s` }}
                              />
                            </div>
                          ) : (
                            <div className={cn(
                              "h-8 w-8 rounded-full border-2 transition-all duration-300",
                              isCurrent ? "border-[#5A32FA] bg-gradient-to-br from-[#5A32FA]/20 to-[#7D2AE8]/20" : "border-gray-300"
                            )}>
                              <Circle 
                                className={cn(
                                  "h-full w-full",
                                  isCurrent ? "text-[#5A32FA]" : "text-gray-400"
                                )} 
                              />
                            </div>
                          )}
                        </div>
                        <div className="min-w-0">
                          <p className={cn(
                            "text-sm font-medium transition-colors",
                            isCurrent ? "text-[#5A32FA]" : isCompleted || isPast ? "text-gray-900" : "text-gray-500"
                          )}>
                            Step {index + 1}
                          </p>
                          <p className={cn(
                            "text-sm transition-colors truncate",
                            isCurrent ? "text-[#5A32FA] font-medium" : "text-gray-600"
                          )}>
                            {step.label}
                          </p>
                        </div>
                      </Link>
                      
                      {index < steps.length - 1 && (
                        <div className="mx-4 w-16">
                          <div className="relative h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className={cn(
                                "absolute inset-y-0 left-0 transition-all duration-500",
                                (isCompleted || isPast) ? "w-full bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8]" : "w-0"
                              )}
                            >
                              {(isCompleted || isPast) && (
                                <div className="absolute right-0 top-0 h-full w-8 bg-white/30 animate-shimmer" />
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
                </div>
              </div>
            </div>
          </BlurFade>

          {/* Content */}
          <BlurFade delay={0.2} inView>
            <div className="rounded-2xl bg-white/80 backdrop-blur-sm p-8 shadow-xl border border-white/20">
              {children}
            </div>
          </BlurFade>
        </div>
      </div>

      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(400%);
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
        
        @keyframes pulse-wave {
          0%, 100% {
            opacity: 0;
            transform: scale(1);
          }
          20% {
            opacity: 0.25;
            transform: scale(1.4);
          }
          40% {
            opacity: 0;
            transform: scale(1.6);
          }
        }
        .animate-pulse-wave {
          animation: pulse-wave 3.5s infinite ease-out;
        }

        /* Custom scrollbar styling */
        .custom-scrollbar::-webkit-scrollbar {
          height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(243, 244, 246, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(90, 50, 250, 0.3);
          border-radius: 3px;
          transition: background 0.3s ease;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(90, 50, 250, 0.5);
        }
        
        /* Firefox scrollbar styling */
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(90, 50, 250, 0.3) rgba(243, 244, 246, 0.5);
        }
      `}</style>
    </div>
  );
}