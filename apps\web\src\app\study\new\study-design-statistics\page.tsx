"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Checkbox } from "~/components/ui/checkbox";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Settings,
  Calculator,
  Target,
  BarChart3,
  TrendingUp,
  Users,
  Brain,
  FileSpreadsheet,
  Plus,
  X
} from "lucide-react";

export default function StudyDesignStatisticsPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    // Basic design parameters (existing)
    designType: store.discovery.design?.designType || "",
    randomizationRatio: store.discovery.design?.randomizationRatio || "",
    blinding: store.discovery.design?.blinding || "",
    controlType: store.discovery.design?.controlType || "",
    
    // Enhanced design details (new)
    designDescriptors: store.discovery.design?.designDescriptors || [],
    interventionModels: store.discovery.design?.interventionModels || [],
    hasActiveComparator: store.discovery.design?.hasActiveComparator ?? false,
    controlMethods: store.discovery.design?.controlMethods || [],
    numberOfStudyArms: store.discovery.design?.numberOfStudyArms || 2,
    studyArmDescriptions: store.discovery.design?.studyArmDescriptions || [],
    analysisPopulations: store.discovery.design?.analysisPopulations || [],
    hasAdaptiveDesign: store.discovery.design?.hasAdaptiveDesign ?? false,
    adaptiveDesignDetails: store.discovery.design?.adaptiveDesignDetails || "",
    
    // Objectives & Endpoints
    primaryAspectAssessing: store.discovery.objectives.primaryAspectAssessing || [],
    primaryGoal: store.discovery.objectives.primaryGoal || "",
    primaryObjectiveStatement: store.discovery.objectives.primaryObjectiveStatement || "",
    secondaryGoals: store.discovery.objectives.secondaryGoals || [],
    secondaryObjectiveStatement: store.discovery.objectives.secondaryObjectiveStatement || "",
    exploratoryObjectives: store.discovery.objectives.exploratoryObjectives || [],
    exploratoryObjectiveAnalyses: store.discovery.objectives.exploratoryObjectiveAnalyses || [],
    
    // Statistical Analysis (new)
    sampleSizeDetermination: store.discovery.statistical?.sampleSizeDetermination || "",
    statisticalModel: store.discovery.statistical?.statisticalModel || "",
    hypothesisAndAnalysis: store.discovery.statistical?.hypothesisAndAnalysis || "",
    interimAnalysisPlan: store.discovery.statistical?.interimAnalysisPlan || "",
    power: store.discovery.statistical?.power || 80,
    alpha: store.discovery.statistical?.alpha || 0.05,
    multipleTesting: store.discovery.statistical?.multipleTesting || "",
    missingDataStrategy: store.discovery.statistical?.missingDataStrategy || "",
  });

  const [newDescriptor, setNewDescriptor] = useState("");
  const [newArmDescription, setNewArmDescription] = useState("");
  const [newExploratoryObjective, setNewExploratoryObjective] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined,
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("study-design-statistics");
      router.push("/study/new/study-population");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    setActiveInsightsPanel(field);
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Analyzing study designs...',
        progressMessages: [],
      }
    }));
    
    const progressUpdates = field === 'study-design' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching study designs...' },
      { delay: 3500, message: 'Analyzing design patterns...' },
      { delay: 6000, message: 'Extracting best practices...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'statistical-analysis' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching statistical methods...' },
      { delay: 3500, message: 'Analyzing analysis plans...' },
      { delay: 6000, message: 'Extracting statistical approaches...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching knowledge base...' },
      { delay: 3500, message: 'Analyzing endpoints...' },
      { delay: 6000, message: 'Extracting patterns...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      drugName: store.discovery.intervention.name || undefined,
    };

    const queries: Record<string, string> = {
      "study-design": `What are typical study designs and methodologies for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
      "statistical-analysis": `What statistical analysis methods are commonly used for ${store.discovery.condition || "clinical"} trials? What are typical sample sizes?`,
      "endpoints": `What are typical primary and secondary endpoints for ${store.discovery.condition || "clinical"} trials?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  // Array management functions
  const addDescriptor = () => {
    if (newDescriptor.trim()) {
      setFormData(prev => ({
        ...prev,
        designDescriptors: [...prev.designDescriptors, newDescriptor.trim()]
      }));
      setNewDescriptor("");
    }
  };

  const removeDescriptor = (index: number) => {
    setFormData(prev => ({
      ...prev,
      designDescriptors: prev.designDescriptors.filter((_, i) => i !== index)
    }));
  };

  const addArmDescription = () => {
    if (newArmDescription.trim()) {
      setFormData(prev => ({
        ...prev,
        studyArmDescriptions: [...prev.studyArmDescriptions, newArmDescription.trim()]
      }));
      setNewArmDescription("");
    }
  };

  const removeArmDescription = (index: number) => {
    setFormData(prev => ({
      ...prev,
      studyArmDescriptions: prev.studyArmDescriptions.filter((_, i) => i !== index)
    }));
  };

  const addExploratoryObjective = () => {
    if (newExploratoryObjective.trim()) {
      setFormData(prev => ({
        ...prev,
        exploratoryObjectives: [...prev.exploratoryObjectives, newExploratoryObjective.trim()]
      }));
      setNewExploratoryObjective("");
    }
  };

  const removeExploratoryObjective = (index: number) => {
    setFormData(prev => ({
      ...prev,
      exploratoryObjectives: prev.exploratoryObjectives.filter((_, i) => i !== index)
    }));
  };

  const handleCheckboxChange = (field: string, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...(prev[field] as string[]), value]
        : (prev[field] as string[]).filter(item => item !== value)
    }));
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Validation
    if (!formData.designType) {
      toast.error("Please select a study design type");
      return;
    }
    
    if (!formData.primaryGoal) {
      toast.error("Please enter the primary objective");
      return;
    }

    if (!formData.primaryObjectiveStatement) {
      toast.error("Please provide a formal primary objective statement");
      return;
    }

    // Update store
    store.updateDiscovery({ 
      design: {
        designType: formData.designType,
        randomizationRatio: formData.randomizationRatio,
        blinding: formData.blinding,
        controlType: formData.controlType,
        designDescriptors: formData.designDescriptors,
        interventionModels: formData.interventionModels,
        hasActiveComparator: formData.hasActiveComparator,
        controlMethods: formData.controlMethods,
        numberOfStudyArms: formData.numberOfStudyArms,
        studyArmDescriptions: formData.studyArmDescriptions,
        analysisPopulations: formData.analysisPopulations,
        hasAdaptiveDesign: formData.hasAdaptiveDesign,
        adaptiveDesignDetails: formData.adaptiveDesignDetails,
      },
      objectives: {
        ...store.discovery.objectives,
        primaryAspectAssessing: formData.primaryAspectAssessing,
        primaryGoal: formData.primaryGoal,
        primaryObjectiveStatement: formData.primaryObjectiveStatement,
        secondaryGoals: formData.secondaryGoals,
        secondaryObjectiveStatement: formData.secondaryObjectiveStatement,
        exploratoryObjectives: formData.exploratoryObjectives,
        exploratoryObjectiveAnalyses: formData.exploratoryObjectiveAnalyses,
      },
      statistical: {
        sampleSizeDetermination: formData.sampleSizeDetermination,
        statisticalModel: formData.statisticalModel,
        hypothesisAndAnalysis: formData.hypothesisAndAnalysis,
        interimAnalysisPlan: formData.interimAnalysisPlan,
        power: formData.power,
        alpha: formData.alpha,
        multipleTesting: formData.multipleTesting,
        missingDataStrategy: formData.missingDataStrategy,
      }
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        design: {
          designType: formData.designType,
          randomizationRatio: formData.randomizationRatio,
          blinding: formData.blinding,
          controlType: formData.controlType,
          designDescriptors: formData.designDescriptors,
          interventionModels: formData.interventionModels,
          hasActiveComparator: formData.hasActiveComparator,
          controlMethods: formData.controlMethods,
          numberOfStudyArms: formData.numberOfStudyArms,
          studyArmDescriptions: formData.studyArmDescriptions,
          analysisPopulations: formData.analysisPopulations,
          hasAdaptiveDesign: formData.hasAdaptiveDesign,
          adaptiveDesignDetails: formData.adaptiveDesignDetails,
        },
        objectives: {
          primaryAspectAssessing: formData.primaryAspectAssessing,
          primaryGoal: formData.primaryGoal,
          primaryObjectiveStatement: formData.primaryObjectiveStatement,
          secondaryGoals: formData.secondaryGoals,
          secondaryObjectiveStatement: formData.secondaryObjectiveStatement,
          exploratoryObjectives: formData.exploratoryObjectives,
          exploratoryObjectiveAnalyses: formData.exploratoryObjectiveAnalyses,
        },
        statistical: {
          sampleSizeDetermination: formData.sampleSizeDetermination,
          statisticalModel: formData.statisticalModel,
          hypothesisAndAnalysis: formData.hypothesisAndAnalysis,
          interimAnalysisPlan: formData.interimAnalysisPlan,
          power: formData.power,
          alpha: formData.alpha,
          multipleTesting: formData.multipleTesting,
          missingDataStrategy: formData.missingDataStrategy,
        }
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    // Implementation for applying insights suggestions
    if (actionableData) {
      const updates: any = {};
      Object.keys(actionableData).forEach(key => {
        if (key !== 'field') {
          updates[actionableData.field] = actionableData.value;
        }
      });
      
      if (Object.keys(updates).length > 0) {
        setFormData(prev => ({ ...prev, ...updates }));
        toast.success("Design updated from insights");
      }
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Study Design & Statistical Analysis
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Define your study methodology, endpoints, and statistical framework
          </p>
        </div>
      </BlurFade>

      {/* Study Design Fundamentals */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Study Design Fundamentals
                </CardTitle>
                <CardDescription>
                  Core design parameters and methodology
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("study-design")}
                onRefresh={() => handleGetInsights("study-design", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "study-design"}
                hasCachedData={!!cachedInsights["study-design"]}
                showRefresh={!!cachedInsights["study-design"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="designType">Study Design Type *</Label>
                <Select
                  value={formData.designType}
                  onValueChange={(value) => setFormData({ ...formData, designType: value })}
                >
                  <SelectTrigger id="designType">
                    <SelectValue placeholder="Select design type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="parallel">Parallel Group</SelectItem>
                    <SelectItem value="crossover">Crossover</SelectItem>
                    <SelectItem value="factorial">Factorial</SelectItem>
                    <SelectItem value="sequential">Sequential</SelectItem>
                    <SelectItem value="adaptive">Adaptive</SelectItem>
                    <SelectItem value="cluster">Cluster Randomized</SelectItem>
                    <SelectItem value="single-arm">Single Arm</SelectItem>
                    <SelectItem value="platform">Platform Trial</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="numberOfStudyArms">Number of Study Arms</Label>
                <Input
                  id="numberOfStudyArms"
                  type="number"
                  min="1"
                  max="10"
                  value={formData.numberOfStudyArms}
                  onChange={(e) => setFormData({ ...formData, numberOfStudyArms: parseInt(e.target.value) || 2 })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="blinding">Blinding Level</Label>
                <Select
                  value={formData.blinding}
                  onValueChange={(value) => setFormData({ ...formData, blinding: value })}
                >
                  <SelectTrigger id="blinding">
                    <SelectValue placeholder="Select blinding" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open-label">Open Label</SelectItem>
                    <SelectItem value="single-blind">Single Blind</SelectItem>
                    <SelectItem value="double-blind">Double Blind</SelectItem>
                    <SelectItem value="triple-blind">Triple Blind</SelectItem>
                    <SelectItem value="quadruple-blind">Quadruple Blind</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="controlType">Control Type</Label>
                <Select
                  value={formData.controlType}
                  onValueChange={(value) => setFormData({ ...formData, controlType: value })}
                >
                  <SelectTrigger id="controlType">
                    <SelectValue placeholder="Select control" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="placebo">Placebo</SelectItem>
                    <SelectItem value="active-comparator">Active Comparator</SelectItem>
                    <SelectItem value="standard-of-care">Standard of Care</SelectItem>
                    <SelectItem value="dose-comparison">Dose Comparison</SelectItem>
                    <SelectItem value="none">No Control</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="randomizationRatio">Randomization Ratio</Label>
              <Input
                id="randomizationRatio"
                placeholder="e.g., 1:1, 2:1, 1:1:1"
                value={formData.randomizationRatio}
                onChange={(e) => setFormData({ ...formData, randomizationRatio: e.target.value })}
              />
            </div>

            {/* Design Descriptors */}
            <div className="space-y-3">
              <Label>Study Design Descriptors</Label>
              <div className="text-sm text-muted-foreground mb-2">
                Add descriptors like "prospective", "placebo-controlled", "randomized", etc.
              </div>
              {formData.designDescriptors.map((descriptor, index) => (
                <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                  <Badge variant="secondary">{descriptor}</Badge>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeDescriptor(index)}
                    className="ml-auto"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Add design descriptor..."
                  value={newDescriptor}
                  onChange={(e) => setNewDescriptor(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addDescriptor())}
                />
                <Button onClick={addDescriptor} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Intervention Models & Comparators */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Intervention Models & Comparators
            </CardTitle>
            <CardDescription>
              Define intervention approach and comparator strategy
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label>Intervention Model(s)</Label>
              <div className="grid grid-cols-2 gap-2">
                {["parallel", "crossover", "factorial", "sequential", "single-group"].map((model) => (
                  <div key={model} className="flex items-center space-x-2">
                    <Checkbox
                      id={`intervention-${model}`}
                      checked={formData.interventionModels.includes(model)}
                      onCheckedChange={(checked) => handleCheckboxChange('interventionModels', model, checked as boolean)}
                    />
                    <Label htmlFor={`intervention-${model}`} className="text-sm capitalize">
                      {model.replace('-', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="hasActiveComparator"
                checked={formData.hasActiveComparator}
                onCheckedChange={(checked) => setFormData({ ...formData, hasActiveComparator: checked })}
              />
              <Label htmlFor="hasActiveComparator">Study involves an active comparator</Label>
            </div>

            <div className="space-y-3">
              <Label>Control Method(s)</Label>
              <div className="grid grid-cols-2 gap-2">
                {["placebo", "active-control", "dose-control", "historical", "no-control"].map((method) => (
                  <div key={method} className="flex items-center space-x-2">
                    <Checkbox
                      id={`control-${method}`}
                      checked={formData.controlMethods.includes(method)}
                      onCheckedChange={(checked) => handleCheckboxChange('controlMethods', method, checked as boolean)}
                    />
                    <Label htmlFor={`control-${method}`} className="text-sm capitalize">
                      {method.replace('-', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Study Arms */}
            <div className="space-y-3">
              <Label>Study Arm Descriptions</Label>
              {formData.studyArmDescriptions.map((description, index) => (
                <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                  <span className="text-sm">{description}</span>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeArmDescription(index)}
                    className="ml-auto"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Describe study arm (e.g., Drug A 10mg daily, Placebo)..."
                  value={newArmDescription}
                  onChange={(e) => setNewArmDescription(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addArmDescription())}
                />
                <Button onClick={addArmDescription} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Objectives & Endpoints */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Study Objectives & Endpoints
                </CardTitle>
                <CardDescription>
                  Define formal study objectives and endpoint assessments
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("endpoints")}
                onRefresh={() => handleGetInsights("endpoints", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "endpoints"}
                hasCachedData={!!cachedInsights["endpoints"]}
                showRefresh={!!cachedInsights["endpoints"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label>Primary Aspect(s) Being Assessed</Label>
              <div className="grid grid-cols-2 gap-2">
                {["safety", "efficacy", "pk", "pd", "immunogenicity"].map((aspect) => (
                  <div key={aspect} className="flex items-center space-x-2">
                    <Checkbox
                      id={`aspect-${aspect}`}
                      checked={formData.primaryAspectAssessing.includes(aspect)}
                      onCheckedChange={(checked) => handleCheckboxChange('primaryAspectAssessing', aspect, checked as boolean)}
                    />
                    <Label htmlFor={`aspect-${aspect}`} className="text-sm uppercase">
                      {aspect}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="primaryGoal">Primary Endpoint</Label>
              <Input
                id="primaryGoal"
                placeholder="e.g., Change in HbA1c from baseline at Week 24"
                value={formData.primaryGoal}
                onChange={(e) => setFormData({ ...formData, primaryGoal: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="primaryObjectiveStatement">Primary Objective Statement *</Label>
              <Textarea
                id="primaryObjectiveStatement"
                placeholder="Formal statistical statement of the primary objective (e.g., To demonstrate superiority of Drug A versus placebo in reducing HbA1c...)"
                rows={3}
                value={formData.primaryObjectiveStatement}
                onChange={(e) => setFormData({ ...formData, primaryObjectiveStatement: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryGoals">Secondary Endpoints</Label>
              <Textarea
                id="secondaryGoals"
                placeholder="List secondary endpoints (one per line or comma-separated)"
                rows={3}
                value={formData.secondaryGoals.join('\n')}
                onChange={(e) => setFormData({ ...formData, secondaryGoals: e.target.value.split('\n').filter(Boolean) })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryObjectiveStatement">Secondary Objective Statement</Label>
              <Textarea
                id="secondaryObjectiveStatement"
                placeholder="Formal statement of secondary objectives..."
                rows={3}
                value={formData.secondaryObjectiveStatement}
                onChange={(e) => setFormData({ ...formData, secondaryObjectiveStatement: e.target.value })}
              />
            </div>

            {/* Exploratory Objectives */}
            <div className="space-y-3">
              <Label>Exploratory/Tertiary Objectives</Label>
              {formData.exploratoryObjectives.map((objective, index) => (
                <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                  <span className="text-sm flex-1">{objective}</span>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeExploratoryObjective(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Add exploratory objective..."
                  value={newExploratoryObjective}
                  onChange={(e) => setNewExploratoryObjective(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addExploratoryObjective())}
                />
                <Button onClick={addExploratoryObjective} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="exploratoryObjectiveAnalyses">Exploratory Objective Analyses</Label>
              <Textarea
                id="exploratoryObjectiveAnalyses"
                placeholder="Describe the statistical analyses planned for exploratory objectives..."
                rows={3}
                value={formData.exploratoryObjectiveAnalyses.join('\n')}
                onChange={(e) => setFormData({ ...formData, exploratoryObjectiveAnalyses: e.target.value.split('\n').filter(Boolean) })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Statistical Analysis */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Statistical Analysis Plan
                </CardTitle>
                <CardDescription>
                  Define statistical methodology and analysis framework
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("statistical-analysis")}
                onRefresh={() => handleGetInsights("statistical-analysis", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "statistical-analysis"}
                hasCachedData={!!cachedInsights["statistical-analysis"]}
                showRefresh={!!cachedInsights["statistical-analysis"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label>Analysis Population(s)</Label>
              <div className="grid grid-cols-2 gap-2">
                {["itt", "per-protocol", "modified-itt", "as-treated", "safety"].map((population) => (
                  <div key={population} className="flex items-center space-x-2">
                    <Checkbox
                      id={`population-${population}`}
                      checked={formData.analysisPopulations.includes(population)}
                      onCheckedChange={(checked) => handleCheckboxChange('analysisPopulations', population, checked as boolean)}
                    />
                    <Label htmlFor={`population-${population}`} className="text-sm uppercase">
                      {population.replace('-', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sampleSizeDetermination">Sample Size Determination Method</Label>
              <Textarea
                id="sampleSizeDetermination"
                placeholder="Describe the method used to determine sample size (assumptions, effect size, power, etc.)..."
                rows={3}
                value={formData.sampleSizeDetermination}
                onChange={(e) => setFormData({ ...formData, sampleSizeDetermination: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="power">Statistical Power (%)</Label>
                <Input
                  id="power"
                  type="number"
                  min="70"
                  max="99"
                  value={formData.power}
                  onChange={(e) => setFormData({ ...formData, power: parseInt(e.target.value) || 80 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="alpha">Alpha Level (Type I Error)</Label>
                <Input
                  id="alpha"
                  type="number"
                  step="0.01"
                  min="0.01"
                  max="0.1"
                  value={formData.alpha}
                  onChange={(e) => setFormData({ ...formData, alpha: parseFloat(e.target.value) || 0.05 })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="statisticalModel">Statistical Model & Hypothesis</Label>
              <Textarea
                id="statisticalModel"
                placeholder="Detail the statistical model, hypothesis (superiority/non-inferiority/equivalence), and primary analysis method..."
                rows={4}
                value={formData.statisticalModel}
                onChange={(e) => setFormData({ ...formData, statisticalModel: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="hypothesisAndAnalysis">Method of Analysis for Primary Objective</Label>
              <Textarea
                id="hypothesisAndAnalysis"
                placeholder="Describe the specific statistical test, model assumptions, and analysis approach..."
                rows={3}
                value={formData.hypothesisAndAnalysis}
                onChange={(e) => setFormData({ ...formData, hypothesisAndAnalysis: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="multipleTesting">Multiple Testing Correction</Label>
                <Select
                  value={formData.multipleTesting}
                  onValueChange={(value) => setFormData({ ...formData, multipleTesting: value })}
                >
                  <SelectTrigger id="multipleTesting">
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bonferroni">Bonferroni</SelectItem>
                    <SelectItem value="holm">Holm</SelectItem>
                    <SelectItem value="hochberg">Hochberg</SelectItem>
                    <SelectItem value="false-discovery-rate">False Discovery Rate</SelectItem>
                    <SelectItem value="none">No Correction</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="missingDataStrategy">Missing Data Strategy</Label>
                <Select
                  value={formData.missingDataStrategy}
                  onValueChange={(value) => setFormData({ ...formData, missingDataStrategy: value })}
                >
                  <SelectTrigger id="missingDataStrategy">
                    <SelectValue placeholder="Select strategy" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="multiple-imputation">Multiple Imputation</SelectItem>
                    <SelectItem value="mixed-model">Mixed Model (MMRM)</SelectItem>
                    <SelectItem value="locf">Last Observation Carried Forward</SelectItem>
                    <SelectItem value="bocf">Baseline Observation Carried Forward</SelectItem>
                    <SelectItem value="complete-case">Complete Case Analysis</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="interimAnalysisPlan">Interim Analysis Plan</Label>
              <Textarea
                id="interimAnalysisPlan"
                placeholder="Describe any planned interim analyses, stopping rules, and Data Safety Monitoring Board reviews..."
                rows={3}
                value={formData.interimAnalysisPlan}
                onChange={(e) => setFormData({ ...formData, interimAnalysisPlan: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Adaptive Design */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Adaptive Design Elements
            </CardTitle>
            <CardDescription>
              Specify any adaptive or novel design features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="hasAdaptiveDesign"
                checked={formData.hasAdaptiveDesign}
                onCheckedChange={(checked) => setFormData({ ...formData, hasAdaptiveDesign: checked })}
              />
              <Label htmlFor="hasAdaptiveDesign">Study includes adaptive or novel design elements</Label>
            </div>

            {formData.hasAdaptiveDesign && (
              <div className="space-y-2">
                <Label htmlFor="adaptiveDesignDetails">Adaptive Design Details</Label>
                <Textarea
                  id="adaptiveDesignDetails"
                  placeholder="Explain the adaptive features (dose escalation, futility stopping, sample size re-estimation, etc.)..."
                  rows={4}
                  value={formData.adaptiveDesignDetails}
                  onChange={(e) => setFormData({ ...formData, adaptiveDesignDetails: e.target.value })}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Navigation */}
      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/investigational-product")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Investigational Product
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Study Population"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewerPortal
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}