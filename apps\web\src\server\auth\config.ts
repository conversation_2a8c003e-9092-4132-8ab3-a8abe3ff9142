import { type DefaultSession, type NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import Discord<PERSON>rovider from "next-auth/providers/discord";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role?: "researcher" | "admin";
    } & DefaultSession["user"];
  }

  interface User {
    role?: "researcher" | "admin";
  }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  providers: [
    // Credentials provider for email/password auth (MVP)
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // For MVP, we'll use a simple check
        // In production, this would validate against a database
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Mock user for development
        if (credentials.email === "<EMAIL>" && credentials.password === "demo123") {
          return {
            id: "demo-user-1",
            email: "<EMAIL>",
            name: "Demo Researcher",
            role: "researcher",
          };
        }

        return null;
      },
    }),
    
    // Google OAuth (recommended for production)
    ...(process.env.AUTH_GOOGLE_ID && process.env.AUTH_GOOGLE_SECRET
      ? [
          GoogleProvider({
            clientId: process.env.AUTH_GOOGLE_ID,
            clientSecret: process.env.AUTH_GOOGLE_SECRET,
          }),
        ]
      : []),
    
    // Discord OAuth (optional)
    ...(process.env.AUTH_DISCORD_ID && process.env.AUTH_DISCORD_SECRET
      ? [
          DiscordProvider({
            clientId: process.env.AUTH_DISCORD_ID,
            clientSecret: process.env.AUTH_DISCORD_SECRET,
          }),
        ]
      : []),
  ],
  
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  
  callbacks: {
    jwt: ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.role = user.role ?? "researcher";
      }
      return token;
    },
    
    session: ({ session, token }) => ({
      ...session,
      user: {
        ...session.user,
        id: token.id as string,
        role: token.role as "researcher" | "admin",
      },
    }),
  },
  
  session: {
    strategy: "jwt",
  },
} satisfies NextAuthConfig;
