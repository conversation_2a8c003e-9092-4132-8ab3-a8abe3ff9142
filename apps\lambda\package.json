{"name": "@trialynx/lambda", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"build": "node build.js", "build:watch": "node build.js --watch", "dev": "tsc --watch", "test": "echo \"Error: no test specified\" && exit 1", "sam:build": "sam build", "sam:local": "sam local start-api --port 3001", "start": "npm run build && npm run sam:local", "validate": "sam validate"}, "keywords": [], "author": "", "license": "ISC", "description": "Lambda functions for TriaLynx Insights", "devDependencies": {"@types/aws-lambda": "^8.10.152", "@types/node": "^24.2.0", "esbuild": "^0.25.8", "typescript": "^5.9.2"}, "dependencies": {"@aws-sdk/client-bedrock-agent-runtime": "^3.864.0", "@aws-sdk/client-bedrock-runtime": "^3.859.0", "@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/client-secrets-manager": "^3.859.0"}}