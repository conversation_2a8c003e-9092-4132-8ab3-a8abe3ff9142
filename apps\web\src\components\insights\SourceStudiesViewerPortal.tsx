"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { SourceStudiesViewer } from "./SourceStudiesViewer";
import type { ComponentProps } from "react";

type SourceStudiesViewerProps = ComponentProps<typeof SourceStudiesViewer>;

export function SourceStudiesViewerPortal(props: SourceStudiesViewerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) {
    return null;
  }

  // Create a portal that renders the viewer at the document body level
  // This ensures it's not affected by any transforms in parent elements
  return createPortal(
    <SourceStudiesViewer {...props} />,
    document.body
  );
}