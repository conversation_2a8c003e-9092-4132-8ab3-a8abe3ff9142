"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Shield,
  AlertTriangle,
  Heart,
  Zap,
  FileWarning,
  Baby,
  Plus,
  X,
  Info
} from "lucide-react";

export default function SafetyAssessmentPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    // Safety collection framework
    willCollectAESAE: store.discovery.safety?.willCollectAESAE ?? true,
    
    // Side effects categorization (critical questions)
    likelySideEffects: store.discovery.safety?.likelySideEffects || [],
    lessLikelySideEffects: store.discovery.safety?.lessLikelySideEffects || [],
    rareButSeriousSideEffects: store.discovery.safety?.rareButSeriousSideEffects || [],
    
    // Reproductive risks (critical questions)
    hasReproductiveRisks: store.discovery.safety?.hasReproductiveRisks ?? false,
    reproductiveRiskDetails: store.discovery.safety?.reproductiveRiskDetails || "",
  });

  const [newLikelySideEffect, setNewLikelySideEffect] = useState("");
  const [newLessLikelySideEffect, setNewLessLikelySideEffect] = useState("");
  const [newRareSeriousSideEffect, setNewRareSeriousSideEffect] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined,
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("safety-assessment");
      router.push("/study/new/study-procedures-operations");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    setActiveInsightsPanel(field);
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Analyzing safety profiles...',
        progressMessages: [],
      }
    }));
    
    const progressUpdates = field === 'safety-profile' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching drug safety data...' },
      { delay: 3500, message: 'Analyzing adverse event patterns...' },
      { delay: 6000, message: 'Extracting safety profiles...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'reproductive-risks' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching reproductive safety data...' },
      { delay: 3500, message: 'Analyzing pregnancy risks...' },
      { delay: 6000, message: 'Extracting reproductive toxicity...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching safety monitoring...' },
      { delay: 3500, message: 'Analyzing AE collection methods...' },
      { delay: 6000, message: 'Extracting best practices...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      drugName: store.discovery.intervention.name || undefined,
      drugClass: store.discovery.intervention.class || undefined,
    };

    const queries: Record<string, string> = {
      "safety-profile": `What are typical adverse events and safety profiles for ${store.discovery.intervention.class || "drugs"} targeting ${store.discovery.condition || "this condition"}?`,
      "reproductive-risks": `What are reproductive safety considerations and pregnancy risks for ${store.discovery.intervention.class || "drugs"} treating ${store.discovery.condition || "this condition"}?`,
      "ae-collection": `What are best practices for adverse event collection and safety monitoring in ${store.discovery.phase || "Phase 2/3"} trials?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  // Array management functions
  const addLikelySideEffect = () => {
    if (newLikelySideEffect.trim()) {
      setFormData(prev => ({
        ...prev,
        likelySideEffects: [...prev.likelySideEffects, newLikelySideEffect.trim()]
      }));
      setNewLikelySideEffect("");
    }
  };

  const removeLikelySideEffect = (index: number) => {
    setFormData(prev => ({
      ...prev,
      likelySideEffects: prev.likelySideEffects.filter((_, i) => i !== index)
    }));
  };

  const addLessLikelySideEffect = () => {
    if (newLessLikelySideEffect.trim()) {
      setFormData(prev => ({
        ...prev,
        lessLikelySideEffects: [...prev.lessLikelySideEffects, newLessLikelySideEffect.trim()]
      }));
      setNewLessLikelySideEffect("");
    }
  };

  const removeLessLikelySideEffect = (index: number) => {
    setFormData(prev => ({
      ...prev,
      lessLikelySideEffects: prev.lessLikelySideEffects.filter((_, i) => i !== index)
    }));
  };

  const addRareSeriousSideEffect = () => {
    if (newRareSeriousSideEffect.trim()) {
      setFormData(prev => ({
        ...prev,
        rareButSeriousSideEffects: [...prev.rareButSeriousSideEffects, newRareSeriousSideEffect.trim()]
      }));
      setNewRareSeriousSideEffect("");
    }
  };

  const removeRareSeriousSideEffect = (index: number) => {
    setFormData(prev => ({
      ...prev,
      rareButSeriousSideEffects: prev.rareButSeriousSideEffects.filter((_, i) => i !== index)
    }));
  };

  const getTotalSideEffects = () => {
    return formData.likelySideEffects.length + 
           formData.lessLikelySideEffects.length + 
           formData.rareButSeriousSideEffects.length;
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Validation - require safety framework decision
    if (formData.willCollectAESAE && getTotalSideEffects() === 0) {
      toast.error("Since you're collecting AEs/SAEs, please specify at least some expected side effects");
      return;
    }

    if (formData.hasReproductiveRisks && !formData.reproductiveRiskDetails.trim()) {
      toast.error("Please provide details about the reproductive risks");
      return;
    }

    // Update store with comprehensive safety data
    store.updateDiscovery({ 
      safety: {
        willCollectAESAE: formData.willCollectAESAE,
        likelySideEffects: formData.likelySideEffects,
        lessLikelySideEffects: formData.lessLikelySideEffects,
        rareButSeriousSideEffects: formData.rareButSeriousSideEffects,
        hasReproductiveRisks: formData.hasReproductiveRisks,
        reproductiveRiskDetails: formData.reproductiveRiskDetails,
      }
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        safety: {
          willCollectAESAE: formData.willCollectAESAE,
          likelySideEffects: formData.likelySideEffects,
          lessLikelySideEffects: formData.lessLikelySideEffects,
          rareButSeriousSideEffects: formData.rareButSeriousSideEffects,
          hasReproductiveRisks: formData.hasReproductiveRisks,
          reproductiveRiskDetails: formData.reproductiveRiskDetails,
        }
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    if (field === "safety-profile") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'likelySideEffects' && actionableData.effects) {
          updates.likelySideEffects = [...new Set([...formData.likelySideEffects, ...actionableData.effects])];
          toast.success("Likely side effects added");
        }
        if (actionableData.field === 'lessLikelySideEffects' && actionableData.effects) {
          updates.lessLikelySideEffects = [...new Set([...formData.lessLikelySideEffects, ...actionableData.effects])];
          toast.success("Less likely side effects added");
        }
        if (actionableData.field === 'rareButSeriousSideEffects' && actionableData.effects) {
          updates.rareButSeriousSideEffects = [...new Set([...formData.rareButSeriousSideEffects, ...actionableData.effects])];
          toast.success("Rare serious side effects added");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "reproductive-risks") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'hasReproductiveRisks') {
          updates.hasReproductiveRisks = actionableData.value;
          toast.success("Reproductive risk status updated");
        }
        if (actionableData.field === 'reproductiveRiskDetails') {
          updates.reproductiveRiskDetails = actionableData.value;
          toast.success("Reproductive risk details updated");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "ae-collection") {
      if (actionableData && actionableData.field === 'willCollectAESAE') {
        setFormData(prev => ({ ...prev, willCollectAESAE: actionableData.value }));
        toast.success("AE/SAE collection plan updated");
      }
      return;
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Safety Assessment
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Define safety monitoring framework and expected risk profile
          </p>
        </div>
      </BlurFade>

      {/* AE/SAE Collection Framework */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Adverse Event Collection Framework
                </CardTitle>
                <CardDescription>
                  Define how safety data will be collected and monitored
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("ae-collection")}
                onRefresh={() => handleGetInsights("ae-collection", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "ae-collection"}
                hasCachedData={!!cachedInsights["ae-collection"]}
                showRefresh={!!cachedInsights["ae-collection"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="willCollectAESAE"
                checked={formData.willCollectAESAE}
                onCheckedChange={(checked) => setFormData({ ...formData, willCollectAESAE: checked })}
              />
              <Label htmlFor="willCollectAESAE" className="text-base">
                Will Adverse Events (AEs) & Serious Adverse Events (SAEs) be collected?
              </Label>
            </div>
            
            {formData.willCollectAESAE && (
              <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium text-blue-900 dark:text-blue-200">Safety Monitoring Active</h4>
                </div>
                <p className="text-sm text-blue-800 dark:text-blue-300">
                  This study will include comprehensive adverse event collection. Please specify the expected safety profile below.
                </p>
              </div>
            )}
            
            {!formData.willCollectAESAE && (
              <div className="rounded-lg bg-amber-50 dark:bg-amber-900/20 p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <h4 className="font-medium text-amber-900 dark:text-amber-200">Limited Safety Monitoring</h4>
                </div>
                <p className="text-sm text-amber-800 dark:text-amber-300">
                  Consider whether this is appropriate for your study type and regulatory requirements.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Expected Safety Profile */}
      {formData.willCollectAESAE && (
        <>
          {/* Likely Side Effects */}
          <BlurFade delay={0.3} inView>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5 text-orange-500" />
                      Likely Side Effects
                    </CardTitle>
                    <CardDescription>
                      List side effects that are likely to occur (common, ≥10% incidence)
                    </CardDescription>
                  </div>
                  <InsightsButton
                    onClick={() => handleGetInsights("safety-profile")}
                    onRefresh={() => handleGetInsights("safety-profile", true)}
                    loading={queryInsights.isPending && queryInsights.variables?.field === "safety-profile"}
                    hasCachedData={!!cachedInsights["safety-profile"]}
                    showRefresh={!!cachedInsights["safety-profile"]}
                  />
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.likelySideEffects.map((effect, index) => (
                  <div key={index} className="flex items-center gap-2 rounded-lg border border-orange-200 bg-orange-50 dark:bg-orange-900/20 p-3">
                    <Zap className="h-4 w-4 text-orange-500" />
                    <span className="flex-1 text-sm">{effect}</span>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => removeLikelySideEffect(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <div className="flex gap-2">
                  <Input
                    placeholder="Add likely side effect (e.g., nausea, headache, fatigue)..."
                    value={newLikelySideEffect}
                    onChange={(e) => setNewLikelySideEffect(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addLikelySideEffect())}
                  />
                  <Button onClick={addLikelySideEffect} size="icon">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  {formData.likelySideEffects.length} likely side effects defined
                </p>
              </CardContent>
            </Card>
          </BlurFade>

          {/* Less Likely Side Effects */}
          <BlurFade delay={0.4} inView>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  Less Likely Side Effects
                </CardTitle>
                <CardDescription>
                  List side effects that are less likely to occur (uncommon, 1-10% incidence)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.lessLikelySideEffects.map((effect, index) => (
                  <div key={index} className="flex items-center gap-2 rounded-lg border border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 p-3">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <span className="flex-1 text-sm">{effect}</span>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => removeLessLikelySideEffect(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <div className="flex gap-2">
                  <Input
                    placeholder="Add less likely side effect (e.g., dizziness, constipation)..."
                    value={newLessLikelySideEffect}
                    onChange={(e) => setNewLessLikelySideEffect(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addLessLikelySideEffect())}
                  />
                  <Button onClick={addLessLikelySideEffect} size="icon">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  {formData.lessLikelySideEffects.length} less likely side effects defined
                </p>
              </CardContent>
            </Card>
          </BlurFade>

          {/* Rare but Serious Side Effects */}
          <BlurFade delay={0.5} inView>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileWarning className="h-5 w-5 text-red-500" />
                  Rare but Serious Side Effects
                </CardTitle>
                <CardDescription>
                  List rare but serious side effects that require special monitoring (&lt;1% but serious)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.rareButSeriousSideEffects.map((effect, index) => (
                  <div key={index} className="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 dark:bg-red-900/20 p-3">
                    <FileWarning className="h-4 w-4 text-red-500" />
                    <span className="flex-1 text-sm">{effect}</span>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => removeRareSeriousSideEffect(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <div className="flex gap-2">
                  <Input
                    placeholder="Add rare serious side effect (e.g., liver toxicity, anaphylaxis)..."
                    value={newRareSeriousSideEffect}
                    onChange={(e) => setNewRareSeriousSideEffect(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addRareSeriousSideEffect())}
                  />
                  <Button onClick={addRareSeriousSideEffect} size="icon">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  {formData.rareButSeriousSideEffects.length} rare serious side effects defined
                </p>
              </CardContent>
            </Card>
          </BlurFade>

          {/* Safety Summary */}
          <BlurFade delay={0.6} inView>
            <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="h-5 w-5 text-gray-600" />
                  <h3 className="font-medium text-gray-900">Safety Profile Summary</h3>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600">{formData.likelySideEffects.length}</p>
                    <p className="text-gray-600">Likely Effects</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-yellow-600">{formData.lessLikelySideEffects.length}</p>
                    <p className="text-gray-600">Less Likely</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-red-600">{formData.rareButSeriousSideEffects.length}</p>
                    <p className="text-gray-600">Rare Serious</p>
                  </div>
                </div>
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    Total: <strong>{getTotalSideEffects()}</strong> defined adverse events across all categories
                  </p>
                </div>
              </CardContent>
            </Card>
          </BlurFade>
        </>
      )}

      {/* Reproductive Risks */}
      <BlurFade delay={0.7} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Baby className="h-5 w-5" />
                  Reproductive Risks Assessment
                </CardTitle>
                <CardDescription>
                  Assess potential reproductive and pregnancy-related risks
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("reproductive-risks")}
                onRefresh={() => handleGetInsights("reproductive-risks", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "reproductive-risks"}
                hasCachedData={!!cachedInsights["reproductive-risks"]}
                showRefresh={!!cachedInsights["reproductive-risks"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="hasReproductiveRisks"
                checked={formData.hasReproductiveRisks}
                onCheckedChange={(checked) => setFormData({ ...formData, hasReproductiveRisks: checked })}
              />
              <Label htmlFor="hasReproductiveRisks" className="text-base">
                Are there any reproductive risks associated with this study?
              </Label>
            </div>

            {formData.hasReproductiveRisks && (
              <div className="space-y-4">
                <div className="rounded-lg bg-amber-50 dark:bg-amber-900/20 p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-amber-600" />
                    <h4 className="font-medium text-amber-900 dark:text-amber-200">Reproductive Risk Assessment Required</h4>
                  </div>
                  <p className="text-sm text-amber-800 dark:text-amber-300">
                    Please provide detailed scientific information about reproductive risks and safety measures.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reproductiveRiskDetails">Scientifically Detail the Reproductive Risks</Label>
                  <Textarea
                    id="reproductiveRiskDetails"
                    placeholder="Provide detailed scientific information about reproductive risks, including animal reproductive toxicity studies, mechanism of action affecting reproduction, contraceptive requirements, pregnancy testing requirements, etc..."
                    rows={6}
                    value={formData.reproductiveRiskDetails}
                    onChange={(e) => setFormData({ ...formData, reproductiveRiskDetails: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Include information about: animal reproductive toxicity studies, teratogenic potential, fertility effects, 
                    contraceptive requirements, pregnancy testing protocols, breastfeeding considerations, and any special 
                    monitoring requirements for reproductive-aged participants.
                  </p>
                </div>
              </div>
            )}

            {!formData.hasReproductiveRisks && (
              <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Heart className="h-4 w-4 text-green-600" />
                  <h4 className="font-medium text-green-900 dark:text-green-200">No Reproductive Risks Identified</h4>
                </div>
                <p className="text-sm text-green-800 dark:text-green-300">
                  This study is assessed as having no significant reproductive risks. Standard safety monitoring will apply.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Navigation */}
      <BlurFade delay={0.8} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/study-population")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Study Population
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Study Procedures & Operations"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewerPortal
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}