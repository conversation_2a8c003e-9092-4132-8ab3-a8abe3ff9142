import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { generateMockStudies } from "~/services/mock/trial-data";
import { env } from "~/env";
import type { StudyResult } from "~/types/trial-design";

export const knowledgeBaseRouter = createTRPCRouter({
  findSimilarStudies: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      query: z.object({
        studyType: z.enum(["drug", "device", "behavioral", "diagnostic", "other"]).optional(),
        condition: z.string().optional(),
        intervention: z.string().optional(),
        phase: z.enum(["phase1", "phase2", "phase3", "phase4", "unknown"]).optional(),
        population: z.object({
          ageMin: z.number().optional(),
          ageMax: z.number().optional(),
          gender: z.enum(["all", "male", "female"]).optional(),
        }).optional(),
      }),
      filters: z.object({
        status: z.array(z.enum(["completed", "active", "recruiting", "terminated", "withdrawn", "suspended"])).optional(),
        minEnrollment: z.number().optional(),
        maxEnrollment: z.number().optional(),
        dateRange: z.object({
          start: z.date().optional(),
          end: z.date().optional(),
        }).optional(),
      }).optional(),
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ input }) => {
      // Check if we should use mock data
      const useMockData = env.NEXT_PUBLIC_USE_MOCK_DATA === "true";
      
      if (useMockData) {
        // Generate mock studies
        const allStudies = generateMockStudies(50);
        
        // Simple filtering based on input
        let filteredStudies = allStudies;
        
        // Filter by study type
        if (input.query.studyType) {
          filteredStudies = filteredStudies.filter(
            s => s.studyType === input.query.studyType
          );
        }
        
        // Filter by phase
        if (input.query.phase) {
          filteredStudies = filteredStudies.filter(
            s => s.phase === input.query.phase
          );
        }
        
        // Filter by status
        if (input.filters?.status && input.filters.status.length > 0) {
          filteredStudies = filteredStudies.filter(
            s => input.filters!.status!.includes(s.status)
          );
        }
        
        // Filter by enrollment
        if (input.filters?.minEnrollment) {
          filteredStudies = filteredStudies.filter(
            s => s.enrollment >= input.filters!.minEnrollment!
          );
        }
        
        if (input.filters?.maxEnrollment) {
          filteredStudies = filteredStudies.filter(
            s => s.enrollment <= input.filters!.maxEnrollment!
          );
        }
        
        // Sort by relevance score
        filteredStudies.sort((a, b) => b.relevanceScore - a.relevanceScore);
        
        // Apply limit
        const results = filteredStudies.slice(0, input.limit);
        
        return {
          studies: results,
          totalFound: filteredStudies.length,
          queryMetadata: {
            searchTerms: [
              input.query.condition,
              input.query.intervention,
              input.query.studyType,
            ].filter(Boolean) as string[],
            filters: input.filters ?? {},
            executionTime: Math.random() * 1000 + 500, // Mock execution time
          },
        };
      } else {
        // TODO: Implement actual AWS Bedrock Knowledge Base query
        // For now, throw an error if AWS is not configured
        throw new Error("AWS integration not yet configured. Please use mock data for testing.");
      }
    }),

  getStudyDetails: publicProcedure
    .input(z.object({
      studyId: z.string(),
    }))
    .query(async ({ input }) => {
      const useMockData = env.NEXT_PUBLIC_USE_MOCK_DATA === "true";
      
      if (useMockData) {
        // Generate a detailed mock study
        const studies = generateMockStudies(1);
        const study = studies[0]!;
        study.id = input.studyId;
        
        // Add more detailed information
        return {
          ...study,
          detailedDescription: `This is a comprehensive clinical trial that aims to evaluate the safety and efficacy of the intervention. The study includes multiple sites across different regions and follows strict protocols for patient enrollment and monitoring. Primary endpoints are measured at regular intervals with comprehensive safety monitoring throughout the trial period.`,
          eligibilityCriteria: {
            inclusion: [
              "Age 18 years or older",
              "Confirmed diagnosis of the target condition",
              "Willing and able to provide informed consent",
              "Adequate organ function as defined by protocol",
            ],
            exclusion: [
              "Pregnant or nursing",
              "Active malignancy",
              "Uncontrolled medical conditions",
              "Previous treatment with similar interventions",
            ],
          },
          sites: [
            { name: "Medical Center A", location: "New York, NY", status: "recruiting" },
            { name: "University Hospital B", location: "Los Angeles, CA", status: "active" },
            { name: "Research Institute C", location: "Chicago, IL", status: "active" },
          ],
        };
      } else {
        throw new Error("AWS integration not yet configured. Please use mock data for testing.");
      }
    }),

  refineSearch: publicProcedure
    .input(z.object({
      studies: z.array(z.any()), // Accept the current studies array
      refinements: z.object({
        sortBy: z.enum(["relevance", "enrollment", "date", "status"]).optional(),
        sortOrder: z.enum(["asc", "desc"]).optional(),
        filters: z.object({
          status: z.array(z.string()).optional(),
          minEnrollment: z.number().optional(),
          maxEnrollment: z.number().optional(),
          phase: z.array(z.string()).optional(),
        }).optional(),
      }),
    }))
    .mutation(async ({ input }) => {
      let refinedStudies = [...input.studies] as StudyResult[];
      
      // Apply filters
      if (input.refinements.filters) {
        const filters = input.refinements.filters;
        
        if (filters.status && filters.status.length > 0) {
          refinedStudies = refinedStudies.filter(
            s => filters.status!.includes(s.status)
          );
        }
        
        if (filters.phase && filters.phase.length > 0) {
          refinedStudies = refinedStudies.filter(
            s => s.phase && filters.phase!.includes(s.phase)
          );
        }
        
        if (filters.minEnrollment !== undefined) {
          refinedStudies = refinedStudies.filter(
            s => s.enrollment >= filters.minEnrollment!
          );
        }
        
        if (filters.maxEnrollment !== undefined) {
          refinedStudies = refinedStudies.filter(
            s => s.enrollment <= filters.maxEnrollment!
          );
        }
      }
      
      // Apply sorting
      if (input.refinements.sortBy) {
        const sortOrder = input.refinements.sortOrder ?? "desc";
        
        refinedStudies.sort((a, b) => {
          let compareValue = 0;
          
          switch (input.refinements.sortBy) {
            case "relevance":
              compareValue = a.relevanceScore - b.relevanceScore;
              break;
            case "enrollment":
              compareValue = a.enrollment - b.enrollment;
              break;
            case "date":
              compareValue = new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
              break;
            case "status":
              compareValue = a.status.localeCompare(b.status);
              break;
          }
          
          return sortOrder === "desc" ? -compareValue : compareValue;
        });
      }
      
      return {
        studies: refinedStudies,
        totalResults: refinedStudies.length,
      };
    }),

  fetchDocument: publicProcedure
    .input(z.object({
      s3Uri: z.string(),
    }))
    .query(async ({ input }) => {
      try {
        // Extract bucket and key from S3 URI
        const s3Match = input.s3Uri.match(/^s3:\/\/([^\/]+)\/(.+)$/);
        if (!s3Match) {
          throw new Error('Invalid S3 URI format');
        }
        
        const [, bucket, key] = s3Match;
        
        // Convert S3 URI to public HTTPS URL
        // s3://trialynx-clinical-trials-gov/text-documents/NCT065/NCT06511908.txt
        // -> https://trialynx-clinical-trials-gov.s3.us-west-2.amazonaws.com/text-documents/NCT065/NCT06511908.txt
        const publicUrl = `https://trialynx-clinical-trials-gov.s3.us-west-2.amazonaws.com/${key}`;
        console.log('Fetching document from public URL:', publicUrl);
        
        // Extract NCT ID from the URI
        const nctMatch = input.s3Uri.match(/NCT\d{8}/);
        const nctId = nctMatch ? nctMatch[0] : 'NCT00000000';
        
        // Fetch the actual document from the public S3 URL
        let documentContent: string;
        try {
          const response = await fetch(publicUrl);
          if (!response.ok) {
            throw new Error(`Failed to fetch document: ${response.statusText}`);
          }
          documentContent = await response.text();
          console.log(`Successfully fetched document ${nctId}, length: ${documentContent.length} characters`);
        } catch (fetchError) {
          console.error('Error fetching document from S3:', fetchError);
          // Fallback to a simple error message
          documentContent = `Clinical Trial: ${nctId}
Title: Error Loading Document
Official Title: Unable to fetch document from S3

================================================================================

ERROR
----------------------------------------
Could not fetch the document content from S3. The document may not be available or there may be a network issue.

S3 URI: ${input.s3Uri}
Public URL: ${publicUrl}
Error: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`;
        }
        
        // Parse the document
        const parsed = parseTrialDocument(documentContent);
        
        return {
          nctId: parsed.nctId || nctId,
          title: parsed.title || `Clinical Trial ${nctId}`,
          content: documentContent,
          parsed: parsed,
        };
      } catch (error) {
        console.error('Error fetching S3 document:', error);
        throw new Error('Failed to fetch document from S3');
      }
    }),

  queryInsights: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      field: z.string(),
      context: z.object({
        studyType: z.string().optional(),
        condition: z.string().optional(),
        drugClass: z.string().optional(),
        isNewCompound: z.boolean().optional(),
        mechanism: z.string().optional(),
        phase: z.string().optional(),
        primaryEndpoint: z.string().optional(),
      }),
      query: z.string(),
    }))
    .mutation(async ({ input }) => {
      // Check if we should use Lambda endpoint
      const useLambda = process.env.LAMBDA_ENDPOINT_URL;
      
      if (useLambda) {
        try {
          // Call the Lambda endpoint
          const response = await fetch(`${useLambda}/query`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: input.query,
              field: input.field,
              context: input.context,
            }),
          });

          if (!response.ok) {
            console.error('Lambda endpoint error:', response.statusText);
            throw new Error(`Lambda endpoint error: ${response.statusText}`);
          }

          const data = await response.json();
          
          // Log what we're getting from Lambda
          console.log('Lambda response sources:', data.sources?.length || 0, 'sources');
          if (data.sources && data.sources.length > 0) {
            console.log('First source:', data.sources[0]);
          }
          
          // Return the sections and sources from the Lambda response
          return { 
            sections: data.sections || [],
            sources: data.sources || [],  // Add sources here!
            citations: data.citations || [],
            output: data.output || '',
            metadata: data.metadata || {},
          };
        } catch (error) {
          console.error('Error calling Lambda endpoint:', error);
          // Fall back to mock data on error
        }
      }
      
      // Mock data fallback
      const mockInsightsByField: Record<string, any> = {
        "phase": {
          sections: [
            {
              title: "Recommended Phase",
              content: input.context.isNewCompound 
                ? "Based on similar novel compounds in this therapeutic area, a Phase 1 study is recommended to establish safety, tolerability, and pharmacokinetics. Most first-in-human studies for this drug class start with single ascending dose (SAD) followed by multiple ascending dose (MAD) cohorts."
                : "For existing compounds being repurposed for " + (input.context.condition || "this indication") + ", Phase 2 studies are typically appropriate to establish proof-of-concept and optimal dosing. Similar studies have shown success with 12-16 week treatment periods.",
              citations: [
                { id: "NCT04123456", title: "Phase 1 Study of Novel SGLT-2 Inhibitor", url: "https://clinicaltrials.gov/study/NCT04123456", relevance: 0.92 },
                { id: "NCT04234567", title: "Safety and Tolerability Study in Healthy Volunteers", url: "https://clinicaltrials.gov/study/NCT04234567", relevance: 0.87 },
              ],
              confidence: 0.85
            },
            {
              title: "Study Design Considerations",
              content: "Similar studies have used adaptive dose escalation designs with safety run-in periods. Consider incorporating pharmacokinetic/pharmacodynamic modeling to optimize dose selection for later phases.",
              citations: [
                { id: "NCT04345678", title: "Adaptive Dose-Finding Study", url: "https://clinicaltrials.gov/study/NCT04345678", relevance: 0.79 },
              ],
              confidence: 0.78
            }
          ]
        },
        "primary-endpoint": {
          sections: [
            {
              title: "Common Primary Endpoints",
              content: `For ${input.context.condition || "this condition"}, the most frequently used primary endpoints include: Change from baseline in disease-specific biomarkers, clinical symptom scores, or functional assessments. Studies typically measure at 12-24 weeks for chronic conditions.`,
              citations: [
                { id: "NCT04456789", title: "Efficacy Study with Biomarker Endpoints", url: "https://clinicaltrials.gov/study/NCT04456789", relevance: 0.88 },
                { id: "NCT04567890", title: "Clinical Outcomes Assessment Trial", url: "https://clinicaltrials.gov/study/NCT04567890", relevance: 0.83 },
              ],
              confidence: 0.82
            }
          ]
        },
        "secondary-endpoints": {
          sections: [
            {
              title: "Recommended Secondary Endpoints",
              content: "Quality of life measures (SF-36, EQ-5D), safety parameters (adverse events, laboratory values), pharmacokinetic parameters, and exploratory biomarkers are commonly included as secondary endpoints.",
              citations: [
                { id: "NCT04678901", title: "Comprehensive Endpoint Assessment Study", url: "https://clinicaltrials.gov/study/NCT04678901", relevance: 0.76 },
              ],
              confidence: 0.74
            }
          ]
        },
        "study-duration": {
          sections: [
            {
              title: "Typical Duration",
              content: `Studies for ${input.context.condition || "similar conditions"} typically run for 12-24 weeks of active treatment with an additional 4-12 weeks of follow-up. Chronic conditions may require longer treatment periods (6-12 months) to demonstrate sustained efficacy.`,
              citations: [
                { id: "NCT04789012", title: "Long-term Safety and Efficacy Trial", url: "https://clinicaltrials.gov/study/NCT04789012", relevance: 0.81 },
              ],
              confidence: 0.79
            }
          ]
        }
      };

      const sections = mockInsightsByField[input.field]?.sections || [
        {
          title: "General Insights",
          content: "Based on analysis of similar studies, we recommend consulting with regulatory authorities for specific guidance on your study design.",
          citations: [],
          confidence: 0.5
        }
      ];

      return { 
        sections,
        sources: [] // Mock data doesn't have sources
      };
    }),
});

// Helper function to parse clinical trial documents
function parseTrialDocument(content: string): any {
  const lines = content.split('\n');
  const parsed: any = {
    sections: [],
  };
  
  // Extract basic fields
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (!line) continue;
    
    if (line.startsWith('Clinical Trial:')) {
      parsed.nctId = line.split(':')[1]?.trim();
    } else if (line.startsWith('Title:') && !parsed.title) {
      parsed.title = line.split(':').slice(1).join(':').trim();
    } else if (line.startsWith('Official Title:')) {
      parsed.officialTitle = line.split(':').slice(1).join(':').trim();
    }
  }
  
  // Parse sections
  let currentSection: any = null;
  let currentContent: string[] = [];
  let inList = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Check for main section headers
    if (line?.match(/^[A-Z][A-Z\s]+$/)) {
      // Save previous section
      if (currentSection) {
        currentSection.content = currentContent.join('\n').trim();
        parsed.sections.push(currentSection);
      }
      
      currentSection = {
        title: line.trim(),
        type: getSectionType(line),
      };
      currentContent = [];
      inList = false;
    } 
    // Check for subsection headers (with dashes)
    else if (line?.match(/^-{3,}$/)) {
      // Skip separator lines
      continue;
    }
    // Add content to current section
    else if (currentSection && line?.trim()) {
      // Handle special fields in STUDY DETAILS
      if (currentSection.title === 'STUDY DETAILS') {
        if (line.includes('Status:')) {
          parsed.status = line.split(':')[1]?.trim();
        } else if (line.includes('Study Type:')) {
          parsed.studyType = line.split(':')[1]?.trim();
        } else if (line.includes('Phase:')) {
          parsed.phase = line.split(':')[1]?.trim();
        } else if (line.includes('Enrollment:')) {
          const enrollmentMatch = line.match(/(\d+)/);
          if (enrollmentMatch && enrollmentMatch[1]) {
            parsed.enrollment = parseInt(enrollmentMatch[1]);
          }
        } else if (line.includes('Start Date:')) {
          parsed.startDate = line.split(':')[1]?.trim();
        } else if (line.includes('Primary Completion Date:')) {
          parsed.primaryCompletionDate = line.split(':')[1]?.trim();
        } else if (line.includes('Study Completion Date:')) {
          parsed.studyCompletionDate = line.split(':')[1]?.trim();
        }
      }
      // Handle ELIGIBILITY section
      else if (currentSection.title === 'ELIGIBILITY') {
        if (line.includes('Age Range:')) {
          if (!parsed.eligibility) parsed.eligibility = {};
          parsed.eligibility.ageRange = line.split(':')[1]?.trim();
        } else if (line.includes('Sex:')) {
          if (!parsed.eligibility) parsed.eligibility = {};
          parsed.eligibility.gender = line.split(':')[1]?.trim();
        } else if (line.includes('Accepts Healthy Volunteers:')) {
          if (!parsed.eligibility) parsed.eligibility = {};
          parsed.eligibility.healthyVolunteers = line.split(':')[1]?.trim() === 'Yes';
        } else if (line.includes('Inclusion Criteria:')) {
          if (!parsed.eligibility) parsed.eligibility = {};
          parsed.eligibility.inclusionCriteria = [];
          inList = true;
          currentSection.listType = 'inclusion';
        } else if (line.includes('Exclusion Criteria:')) {
          if (!parsed.eligibility) parsed.eligibility = {};
          parsed.eligibility.exclusionCriteria = [];
          inList = true;
          currentSection.listType = 'exclusion';
        } else if (inList && line.match(/^\d+\./)) {
          const criteria = line.replace(/^\d+\.\s*/, '').trim();
          if (currentSection.listType === 'inclusion') {
            parsed.eligibility.inclusionCriteria.push(criteria);
          } else if (currentSection.listType === 'exclusion') {
            parsed.eligibility.exclusionCriteria.push(criteria);
          }
        }
      }
      // Handle CONDITIONS section
      else if (currentSection.title === 'CONDITIONS AND KEYWORDS') {
        if (line.includes('This study focuses on:')) {
          parsed.conditions = [line.split(':')[1]?.trim()];
        } else if (line.includes('Keywords:')) {
          const keywords = line.split(':')[1]?.trim();
          if (keywords && keywords !== 'None specified') {
            parsed.keywords = keywords.split(',').map(k => k.trim());
          }
        }
      }
      // Handle SPONSOR section
      else if (currentSection.title === 'SPONSOR AND COLLABORATORS') {
        if (line.includes('Lead Sponsor:')) {
          parsed.sponsor = line.split(':')[1]?.trim();
        } else if (line.includes('Sponsor Type:')) {
          parsed.sponsorType = line.split(':')[1]?.trim();
        }
      }
      // Handle LOCATIONS section
      else if (currentSection.title === 'LOCATIONS') {
        if (line.startsWith('- ')) {
          if (!parsed.locations) parsed.locations = [];
          parsed.locations.push({
            name: line.substring(2).trim(),
          });
        }
      }
      
      currentContent.push(line);
    }
  }
  
  // Save last section
  if (currentSection) {
    currentSection.content = currentContent.join('\n').trim();
    parsed.sections.push(currentSection);
  }
  
  // Process OUTCOMES section specially
  const outcomesSection = parsed.sections.find((s: any) => s.title === 'OUTCOMES');
  if (outcomesSection) {
    const outcomes = parseOutcomes(outcomesSection.content);
    parsed.primaryOutcomes = outcomes.primary;
    parsed.secondaryOutcomes = outcomes.secondary;
  }
  
  // Process ARMS AND GROUPS section
  const armsSection = parsed.sections.find((s: any) => s.title === 'ARMS AND GROUPS');
  if (armsSection) {
    parsed.armsAndGroups = parseArmsAndGroups(armsSection.content);
  }
  
  return parsed;
}

function getSectionType(title: string): string {
  const typeMap: Record<string, string> = {
    'OVERVIEW': 'overview',
    'STUDY DETAILS': 'details',
    'DESCRIPTION': 'description',
    'CONDITIONS AND KEYWORDS': 'conditions',
    'ELIGIBILITY': 'eligibility',
    'OUTCOMES': 'outcomes',
    'STUDY DESIGN': 'design',
    'ARMS AND GROUPS': 'arms',
    'LOCATIONS': 'locations',
    'SPONSOR AND COLLABORATORS': 'sponsor',
    'RESULTS': 'results',
  };
  
  return typeMap[title] || 'information';
}

function parseOutcomes(content: string): { primary: any[], secondary: any[] } {
  const lines = content.split('\n');
  const outcomes: { primary: any[], secondary: any[] } = { primary: [], secondary: [] };
  let currentType: 'primary' | 'secondary' | null = null;
  let currentOutcome: any = null;
  
  for (const line of lines) {
    if (line.includes('Primary Outcomes:')) {
      currentType = 'primary';
    } else if (line.includes('Secondary Outcomes:')) {
      currentType = 'secondary';
    } else if (line.startsWith('- ') && currentType) {
      if (currentOutcome && currentType) {
        outcomes[currentType].push(currentOutcome);
      }
      const name = line.substring(2).split('(')[0]?.trim();
      currentOutcome = { name };
    } else if (line.includes('Description:') && currentOutcome) {
      currentOutcome.description = line.split(':')[1]?.trim();
    } else if (line.includes('Time Frame:') && currentOutcome) {
      currentOutcome.timeFrame = line.split(':')[1]?.trim();
    }
  }
  
  if (currentOutcome && currentType) {
    outcomes[currentType].push(currentOutcome);
  }
  
  return outcomes;
}

function parseArmsAndGroups(content: string): any[] {
  const lines = content.split('\n');
  const arms = [];
  let currentArm: any = null;
  
  for (const line of lines) {
    if (line.startsWith('- ')) {
      if (currentArm) {
        arms.push(currentArm);
      }
      const parts = line.substring(2).split('(');
      currentArm = {
        name: parts[0]?.trim(),
        type: parts[1]?.replace(')', '').trim() || 'Unknown',
        description: '',
      };
    } else if (currentArm && line.trim() && !line.startsWith('-')) {
      currentArm.description = line.trim();
    }
  }
  
  if (currentArm) {
    arms.push(currentArm);
  }
  
  return arms;
}