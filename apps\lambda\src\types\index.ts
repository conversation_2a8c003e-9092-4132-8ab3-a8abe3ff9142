// Core domain types for TriaLynx Insights

// Study Types
export type StudyType = 'drug' | 'device' | 'behavioral' | 'diagnostic' | 'other';
export type StudyPhase = 'phase1' | 'phase2' | 'phase3' | 'phase4' | 'unknown';
export type StudyStatus = 'completed' | 'active' | 'recruiting' | 'terminated' | 'withdrawn' | 'suspended';
export type Gender = 'all' | 'male' | 'female';

// Discovery Phase Types
export interface InterventionDetails {
  name?: string;
  category?: string;
  mechanism?: string;
  class?: string;
  isNewCompound?: boolean;
  deviceClass?: string;
  
  // Enhanced drug product information
  medicalProblem?: string;
  comparisonToExistingTreatments?: string;
  regulatoryStatus?: string;
  activeIngredients?: string[];
  inactiveIngredients?: string[];
  ingredientRationale?: string;
  preclinicalStudies?: string;
  toxicityStudies?: string;
  clinicalTrialsHistory?: string;
  keyFindings?: string;
  pregnancySafety?: 'safe' | 'unsafe' | 'unknown' | 'contraindicated';
  pregnancySafetyDetails?: string;
  fdaApprovalStatus?: 'approved' | 'investigational' | 'compassionate' | 'off-label';
  drugCompoundNumber?: string;
  indApplicationNumber?: string;
}

export interface PopulationCriteria {
  ageMin?: number;
  ageMax?: number;
  gender: Gender;
  specificPopulation?: string;
  inclusionCriteria?: string[];
  exclusionCriteria?: string[];
  
  // Enhanced population details
  targetEnrollment?: string;
  geographicScope?: 'local' | 'national' | 'international';
  healthyVolunteers?: boolean;
  trialAssignmentMethod?: 'randomization' | 'stratification' | 'randomization-stratification' | 'no-assignment';
  numberOfSites?: string;
  siteDistribution?: string;
  countriesEngaged?: string[];
  sitesPerCountry?: Record<string, number>;
}

export interface StudyObjectives {
  primaryGoal: string;
  keyOutcome: string;
  secondaryGoals?: string[];
  
  // Enhanced objectives and timeline
  studyDuration?: string;
  followUpPeriod?: string;
  primaryAspectAssessing?: 'safety' | 'efficacy' | 'pk' | 'pd' | 'immunogenicity'[];
  
  // Formal statistical objectives
  primaryObjectiveStatement?: string;
  secondaryObjectiveStatement?: string;
  exploratoryObjectives?: string[];
  exploratoryObjectiveAnalyses?: string[];
}

// Study Protocol Information
export interface StudyProtocol {
  protocolAcronym?: string;
  protocolFullTitle?: string;
  studyBackground?: string;
  studyDetailsForAI?: string;
  protocolIdNumber?: string;
  trialInterventionDetails?: string;
  studyEventsAndActivities?: string;
  durationWithDates?: string;
}

// Study Design Information
export interface StudyDesignDetails {
  designDescriptors?: string[];
  interventionModels?: string[];
  hasActiveComparator?: boolean;
  controlMethods?: string[];
  numberOfStudyArms?: number;
  studyArmDescriptions?: string[];
  analysisPopulations?: ('itt' | 'per-protocol' | 'modified-itt' | 'as-treated')[];
  hasAdaptiveDesign?: boolean;
  adaptiveDesignDetails?: string;
  
  // Design parameters (existing but enhanced)
  designType?: string;
  randomizationRatio?: string;
  blinding?: string;
  controlType?: string;
}

// Statistical Analysis Information
export interface StatisticalAnalysis {
  sampleSizeDetermination?: string;
  statisticalModel?: string;
  hypothesisAndAnalysis?: string;
  interimAnalysisPlan?: string;
  power?: number;
  alpha?: number;
  multipleTesting?: string;
  missingDataStrategy?: string;
}

// Safety Assessment Information
export interface SafetyAssessment {
  willCollectAESAE?: boolean;
  likelySideEffects?: string[];
  lessLikelySideEffects?: string[];
  rareButSeriousSideEffects?: string[];
  hasReproductiveRisks?: boolean;
  reproductiveRiskDetails?: string;
}

// Laboratory and Biomarker Information
export interface LaboratoryStudies {
  willCollectBiologicalSamples?: boolean;
  biologicalSpecimens?: string[];
  collectionAndProcessing?: string;
  willConductPK?: boolean;
  willConductBiomarker?: boolean;
  willConductImmunogenicity?: boolean;
  willConductGeneticTesting?: boolean;
}

// Timeline and Visit Information
export interface StudyTimeline {
  screeningPeriod?: string;
  baselinePeriod?: string;
  treatmentPeriod?: string;
  followUpPeriod?: string;
  totalDuration?: string;
  visits?: Array<{
    name: string;
    timepoint: string;
    procedures: string[];
    critical?: boolean;
  }>;
}

// Operational Information
export interface OperationalDetails {
  recruitmentRate?: string;
  screenFailureRate?: string;
  dropoutRate?: string;
  dataManagementSystem?: 'edc' | 'paper' | 'hybrid';
  edcCTMSName?: string;
  monitoringApproach?: 'on-site' | 'remote' | 'risk-based' | 'hybrid';
}

// Regulatory, Financial, and Legal Information
export interface RegulatoryFinancialLegal {
  // Regulatory
  irbEthicsCommitteeName?: string;
  sponsorName?: string;
  drugManufacturerName?: string;
  drugManufacturerAddress?: string;
  willUseCRO?: boolean;
  croNameAndAddress?: string;
  dataEvaluationCommittees?: string[];
  independentCommittees?: string[];
  
  // Financial
  willCompensateParticipants?: boolean;
  compensationDetails?: string;
  whoWillPay?: string[];
}

export interface DiscoveryData {
  studyType: StudyType;
  phase?: StudyPhase;
  intervention: InterventionDetails;
  condition: string;
  population: PopulationCriteria;
  objectives: StudyObjectives;
  
  // New comprehensive sections
  protocol?: StudyProtocol;
  design?: StudyDesignDetails;
  statistical?: StatisticalAnalysis;
  safety?: SafetyAssessment;
  laboratory?: LaboratoryStudies;
  timeline?: StudyTimeline;
  operational?: OperationalDetails;
  regulatory?: RegulatoryFinancialLegal;
}

// Knowledge Base Types
export interface BedrockQueryRequest {
  query: string;
  filters?: {
    startDate?: string;
    endDate?: string;
    studyType?: StudyType;
    phase?: StudyPhase;
    status?: StudyStatus[];
    minEnrollment?: number;
    maxEnrollment?: number;
  };
  maxResults?: number;
  includeTerminated?: boolean;
}

export interface StudyResult {
  id: string;
  nctId: string;
  title: string;
  description: string;
  studyType: StudyType;
  phase?: StudyPhase;
  status: StudyStatus;
  startDate: string;
  completionDate?: string;
  primaryCompletionDate?: string;
  enrollment: number;
  actualEnrollment?: number;
  
  // Relevance
  relevanceScore: number;
  matchedTerms: string[];
  highlights: string[];
  
  // Study details
  conditions: string[];
  interventions: string[];
  sponsors: string[];
  locations: string[];
  
  // Outcomes
  primaryOutcomeMeasures: OutcomeMeasure[];
  secondaryOutcomeMeasures: OutcomeMeasure[];
  
  // Design
  studyDesign: StudyDesign;
  
  // Results (if available)
  results?: StudyResults;
}

export interface OutcomeMeasure {
  measure: string;
  timeFrame: string;
  description?: string;
}

export interface StudyDesign {
  allocation?: string;
  interventionModel?: string;
  primaryPurpose?: string;
  masking?: string;
  whoMasked?: string[];
  designType?: string;
}

export interface StudyResults {
  participantFlow?: ParticipantFlow;
  baselineCharacteristics?: any;
  outcomeMeasures?: any;
  adverseEvents?: AdverseEvents;
}

export interface ParticipantFlow {
  enrolled: number;
  started: number;
  completed: number;
  withdrawnReasons?: Record<string, number>;
}

export interface AdverseEvents {
  serious: number;
  other: number;
  deaths: number;
}

export interface SourceDocument {
  nctId: string;
  title: string;
  s3Uri: string;
  excerpt: string;
  score?: number;
}

export interface BedrockQueryResponse {
  output?: string;
  results?: StudyResult[];
  sections?: any[];
  sources?: SourceDocument[];
  citations?: any[];
  sessionId?: string;
  metadata?: {
    totalResults?: number;
    queryTime?: number;
    searchTerms?: string[];
    appliedFilters?: Record<string, any>;
    modelUsed?: string;
  };
}